#!/usr/bin/env python3
"""
Setup script for the Agentic Code Generation System.
This script helps users set up the development environment.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
from typing import List, Optional


class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


def print_colored(message: str, color: str = Colors.OKBLUE) -> None:
    """Print colored message to terminal."""
    print(f"{color}{message}{Colors.ENDC}")


def print_header(message: str) -> None:
    """Print header message."""
    print_colored(f"\n{'='*60}", Colors.HEADER)
    print_colored(f"{message}", Colors.HEADER)
    print_colored(f"{'='*60}", Colors.HEADER)


def print_success(message: str) -> None:
    """Print success message."""
    print_colored(f"✓ {message}", Colors.OKGREEN)


def print_warning(message: str) -> None:
    """Print warning message."""
    print_colored(f"⚠ {message}", Colors.WARNING)


def print_error(message: str) -> None:
    """Print error message."""
    print_colored(f"✗ {message}", Colors.FAIL)


def run_command(command: List[str], cwd: Optional[Path] = None, check: bool = True) -> subprocess.CompletedProcess:
    """Run a command and return the result."""
    try:
        print_colored(f"Running: {' '.join(command)}", Colors.OKCYAN)
        result = subprocess.run(command, cwd=cwd, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {' '.join(command)}")
        if e.stderr:
            print_error(e.stderr)
        if check:
            sys.exit(1)
        return e


def check_python_version() -> bool:
    """Check if Python version is 3.11 or higher."""
    version = sys.version_info
    if version.major == 3 and version.minor >= 11:
        print_success(f"Python {version.major}.{version.minor}.{version.micro} detected")
        return True
    else:
        print_error(f"Python 3.11+ required, but {version.major}.{version.minor}.{version.micro} found")
        return False


def check_node_version() -> bool:
    """Check if Node.js version is 18 or higher."""
    try:
        result = run_command(["node", "--version"], check=False)
        if result.returncode == 0:
            version_str = result.stdout.strip().lstrip('v')
            major_version = int(version_str.split('.')[0])
            if major_version >= 18:
                print_success(f"Node.js {version_str} detected")
                return True
            else:
                print_error(f"Node.js 18+ required, but {version_str} found")
                return False
    except (subprocess.CalledProcessError, ValueError, FileNotFoundError):
        print_error("Node.js not found. Please install Node.js 18+")
        return False


def check_docker() -> bool:
    """Check if Docker is available."""
    try:
        result = run_command(["docker", "--version"], check=False)
        if result.returncode == 0:
            print_success("Docker detected")
            return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print_warning("Docker not found. Docker is optional but recommended for databases")
        return False


def setup_environment_file() -> None:
    """Set up the .env file from .env.example."""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_example.exists():
        print_error(".env.example file not found")
        return
    
    if env_file.exists():
        response = input("⚠ .env file already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print_colored("Skipping .env file setup", Colors.WARNING)
            return
    
    shutil.copy(env_example, env_file)
    print_success(".env file created from .env.example")
    print_colored("Please edit .env file with your API keys and configuration", Colors.WARNING)


def setup_backend() -> bool:
    """Set up the Python backend."""
    print_header("Setting up Backend")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print_error("Backend directory not found")
        return False
    
    # Create virtual environment
    venv_dir = backend_dir / "venv"
    if not venv_dir.exists():
        print_colored("Creating virtual environment...", Colors.OKBLUE)
        run_command([sys.executable, "-m", "venv", "venv"], cwd=backend_dir)
    
    # Determine activation script
    if platform.system() == "Windows":
        activate_script = venv_dir / "Scripts" / "activate"
        python_executable = venv_dir / "Scripts" / "python"
        pip_executable = venv_dir / "Scripts" / "pip"
    else:
        activate_script = venv_dir / "bin" / "activate"
        python_executable = venv_dir / "bin" / "python"
        pip_executable = venv_dir / "bin" / "pip"
    
    # Install dependencies
    print_colored("Installing Python dependencies...", Colors.OKBLUE)
    run_command([str(pip_executable), "install", "--upgrade", "pip"], cwd=backend_dir)
    run_command([str(pip_executable), "install", "-r", "requirements.txt"], cwd=backend_dir)
    
    print_success("Backend setup completed")
    return True


def setup_frontend() -> bool:
    """Set up the TypeScript frontend."""
    print_header("Setting up Frontend")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print_error("Frontend directory not found")
        return False
    
    # Install dependencies
    print_colored("Installing Node.js dependencies...", Colors.OKBLUE)
    run_command(["npm", "install"], cwd=frontend_dir)
    
    # Compile TypeScript
    print_colored("Compiling TypeScript...", Colors.OKBLUE)
    run_command(["npm", "run", "compile"], cwd=frontend_dir)
    
    print_success("Frontend setup completed")
    return True


def setup_databases_docker() -> bool:
    """Set up databases using Docker."""
    print_header("Setting up Databases with Docker")
    
    if not check_docker():
        return False
    
    print_colored("Starting databases with Docker Compose...", Colors.OKBLUE)
    
    # Start only the database services
    services = ["chromadb", "neo4j", "redis"]
    for service in services:
        try:
            run_command(["docker-compose", "up", "-d", service])
            print_success(f"{service} started")
        except subprocess.CalledProcessError:
            print_warning(f"Failed to start {service}")
    
    print_success("Database setup completed")
    return True


def verify_setup() -> None:
    """Verify the setup by testing connections."""
    print_header("Verifying Setup")
    
    # Test backend health (if running)
    try:
        import requests
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print_success("Backend is running and healthy")
        else:
            print_warning("Backend is running but not healthy")
    except Exception:
        print_warning("Backend is not running (this is normal if you haven't started it yet)")
    
    # Check if databases are accessible
    database_ports = {
        "ChromaDB": 8001,
        "Neo4j": 7474,
        "Redis": 6379
    }
    
    for db_name, port in database_ports.items():
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print_success(f"{db_name} is accessible on port {port}")
            else:
                print_warning(f"{db_name} is not accessible on port {port}")
        except Exception:
            print_warning(f"Could not check {db_name} connectivity")


def print_next_steps() -> None:
    """Print next steps for the user."""
    print_header("Next Steps")
    
    steps = [
        "1. Edit the .env file with your API keys:",
        "   - Add your OpenAI API key (OPENAI_API_KEY)",
        "   - Add your Anthropic API key (ANTHROPIC_API_KEY)",
        "",
        "2. Start the backend server:",
        "   cd backend",
        "   source venv/bin/activate  # On Windows: venv\\Scripts\\activate",
        "   python -m uvicorn src.main:app --reload",
        "",
        "3. Install the VS Code extension:",
        "   - Open VS Code",
        "   - Open the frontend folder",
        "   - Press F5 to launch Extension Development Host",
        "",
        "4. Test the system:",
        "   - Open a code file in VS Code",
        "   - Try Ctrl+Alt+G to generate code",
        "   - Try Ctrl+Alt+A to analyze code",
        "",
        "5. Read the documentation:",
        "   - docs/getting-started.md",
        "   - docs/architecture.md",
    ]
    
    for step in steps:
        if step.startswith(("1.", "2.", "3.", "4.", "5.")):
            print_colored(step, Colors.OKGREEN)
        elif step.strip() and not step.startswith(" "):
            print_colored(step, Colors.OKBLUE)
        else:
            print(step)


def main() -> None:
    """Main setup function."""
    print_colored("🤖 Agentic Code Generation System Setup", Colors.HEADER)
    print_colored("This script will help you set up the development environment.", Colors.OKBLUE)
    
    # Check prerequisites
    print_header("Checking Prerequisites")
    
    python_ok = check_python_version()
    node_ok = check_node_version()
    docker_available = check_docker()
    
    if not python_ok or not node_ok:
        print_error("Prerequisites not met. Please install the required software.")
        sys.exit(1)
    
    # Setup environment file
    setup_environment_file()
    
    # Setup components
    backend_ok = setup_backend()
    frontend_ok = setup_frontend()
    
    if docker_available:
        response = input("\n🐳 Set up databases with Docker? (Y/n): ")
        if response.lower() != 'n':
            setup_databases_docker()
    
    # Verify setup
    verify_setup()
    
    # Print next steps
    print_next_steps()
    
    if backend_ok and frontend_ok:
        print_success("\n🎉 Setup completed successfully!")
    else:
        print_warning("\n⚠ Setup completed with some issues. Check the output above.")


if __name__ == "__main__":
    main()
