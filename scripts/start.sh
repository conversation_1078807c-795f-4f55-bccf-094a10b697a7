#!/bin/bash

# Start script for the Agentic Code Generation System
# This script starts all components in the correct order

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to wait for a service to be ready
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Function to start databases with Docker
start_databases() {
    print_status "Starting databases with Docker..."
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose not found. Please install Docker and Docker Compose."
        return 1
    fi
    
    # Start database services
    docker-compose up -d chromadb neo4j redis
    
    # Wait for services to be ready
    wait_for_service localhost 8001 "ChromaDB"
    wait_for_service localhost 7474 "Neo4j"
    wait_for_service localhost 6379 "Redis"
    
    print_success "All databases are running!"
}

# Function to start the backend
start_backend() {
    print_status "Starting backend server..."
    
    cd backend
    
    # Check if virtual environment exists
    if [ ! -d "venv" ]; then
        print_error "Virtual environment not found. Please run setup.py first."
        return 1
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Check if .env file exists
    if [ ! -f "../.env" ]; then
        print_warning ".env file not found. Using default configuration."
    fi
    
    # Start the server in background
    print_status "Starting FastAPI server on http://localhost:8000"
    python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload &
    BACKEND_PID=$!
    
    # Wait for backend to be ready
    wait_for_service localhost 8000 "Backend API"
    
    cd ..
    return 0
}

# Function to check backend health
check_backend_health() {
    print_status "Checking backend health..."
    
    if command -v curl &> /dev/null; then
        response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/health)
        if [ "$response" = "200" ]; then
            print_success "Backend is healthy!"
            return 0
        else
            print_error "Backend health check failed (HTTP $response)"
            return 1
        fi
    else
        print_warning "curl not found. Skipping health check."
        return 0
    fi
}

# Function to open VS Code with the extension
open_vscode() {
    print_status "Opening VS Code with the extension..."
    
    if command -v code &> /dev/null; then
        cd frontend
        code .
        print_success "VS Code opened. Press F5 to launch the extension development host."
        cd ..
    else
        print_warning "VS Code 'code' command not found. Please open VS Code manually."
        print_status "To install the extension:"
        print_status "1. Open VS Code"
        print_status "2. Open the frontend folder"
        print_status "3. Press F5 to launch Extension Development Host"
    fi
}

# Function to display running services
show_services() {
    echo ""
    print_success "🚀 Agentic Code Generation System is running!"
    echo ""
    echo "Services:"
    echo "  📊 Backend API:     http://localhost:8000"
    echo "  📊 API Docs:        http://localhost:8000/docs"
    echo "  🔍 ChromaDB:        http://localhost:8001"
    echo "  🕸️  Neo4j Browser:   http://localhost:7474"
    echo "  ⚡ Redis:           localhost:6379"
    echo ""
    echo "VS Code Extension:"
    echo "  📝 Open the frontend folder in VS Code"
    echo "  🔧 Press F5 to launch Extension Development Host"
    echo ""
    echo "Useful commands:"
    echo "  📋 Check logs:      docker-compose logs -f"
    echo "  🛑 Stop services:   ./scripts/stop.sh"
    echo "  🔄 Restart:         ./scripts/restart.sh"
    echo ""
}

# Function to cleanup on exit
cleanup() {
    print_status "Cleaning up..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    echo "🤖 Starting Agentic Code Generation System..."
    echo ""
    
    # Check if we're in the right directory
    if [ ! -f "README.md" ] || [ ! -d "backend" ] || [ ! -d "frontend" ]; then
        print_error "Please run this script from the project root directory."
        exit 1
    fi
    
    # Parse command line arguments
    START_DATABASES=true
    START_BACKEND=true
    OPEN_VSCODE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-db)
                START_DATABASES=false
                shift
                ;;
            --no-backend)
                START_BACKEND=false
                shift
                ;;
            --vscode)
                OPEN_VSCODE=true
                shift
                ;;
            --help)
                echo "Usage: $0 [options]"
                echo ""
                echo "Options:"
                echo "  --no-db       Skip starting databases"
                echo "  --no-backend  Skip starting backend"
                echo "  --vscode      Open VS Code after starting services"
                echo "  --help        Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Start databases
    if [ "$START_DATABASES" = true ]; then
        start_databases || {
            print_error "Failed to start databases"
            exit 1
        }
    else
        print_status "Skipping database startup"
    fi
    
    # Start backend
    if [ "$START_BACKEND" = true ]; then
        start_backend || {
            print_error "Failed to start backend"
            exit 1
        }
        
        # Check backend health
        sleep 3
        check_backend_health || {
            print_warning "Backend health check failed, but continuing..."
        }
    else
        print_status "Skipping backend startup"
    fi
    
    # Open VS Code if requested
    if [ "$OPEN_VSCODE" = true ]; then
        open_vscode
    fi
    
    # Show running services
    show_services
    
    # Keep the script running
    if [ "$START_BACKEND" = true ]; then
        print_status "Press Ctrl+C to stop all services"
        wait $BACKEND_PID
    fi
}

# Run main function
main "$@"
