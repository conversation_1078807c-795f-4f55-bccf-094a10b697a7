# Agentic Code Assistant - Test Workspace

This is a test workspace for demonstrating the Agentic Code Assistant VS Code extension.

## How to Test the Extension

### 1. Install the Extension
- In VS Code, press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
- Type "Extensions: Install from VSIX"
- Select the `agentic-code-assistant-1.0.0.vsix` file from the frontend directory

### 2. Test Code Generation
- Open `sample.py`
- Press `Ctrl+Alt+G` (or `Cmd+Alt+G` on Mac)
- Enter a prompt like: "Create a function to calculate factorial"
- Watch as the AI generates code at your cursor position

### 3. Test Code Analysis
- Select some code in `sample.py`
- Press `Ctrl+Alt+A` (or `Cmd+Alt+A` on Mac)
- View the analysis results in a side panel

### 4. Test Code Improvement
- Select code you want to improve
- Right-click and select "Agentic: Improve Code"
- Choose improvement type (general, performance, security, style)

### 5. Test Chat Interface
- Press `Ctrl+Alt+C` (or `Cmd+Alt+C` on Mac)
- Ask questions about your code or request assistance
- The AI will provide contextual help

### 6. Test Context Search
- Press `Ctrl+Shift+P` and type "Agentic: Search Context"
- Search for relevant code patterns or information

## Features Demonstrated

✅ **Multi-Agent Code Generation**
✅ **Tool Calling Support** 
✅ **Model Context Protocol**
✅ **Brain-like Memory System**
✅ **Real-time Code Analysis**
✅ **Context-Aware Assistance**
✅ **Team Collaboration Ready**

## Backend Connection

Make sure the backend server is running at `http://localhost:8000` for full functionality.

You can test the connection by checking the extension's output panel for any connection errors.
