# Sample Python file for testing the Agentic Code Assistant

def hello_world():
    """A simple hello world function."""
    print("Hello, World!")

def add_numbers(a, b):
    """Add two numbers together."""
    return a + b

# TODO: Add more complex functions here
# Try using the Agentic extension:
# 1. Press Ctrl+Alt+G to generate code
# 2. Press Ctrl+Alt+A to analyze this code
# 3. Press Ctrl+Alt+C to open the chat interface

if __name__ == "__main__":
    hello_world()
    result = add_numbers(5, 3)
    print(f"5 + 3 = {result}")
