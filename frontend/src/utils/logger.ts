/**
 * Logging utility for the Agentic Code Assistant extension.
 */

import * as vscode from 'vscode';

export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}

export class Logger {
    private static outputChannel: vscode.OutputChannel;
    private static logLevel: LogLevel = LogLevel.INFO;

    static initialize(): void {
        this.outputChannel = vscode.window.createOutputChannel('Agentic Code Assistant');
    }

    static setLogLevel(level: LogLevel): void {
        this.logLevel = level;
    }

    static debug(message: string, ...args: any[]): void {
        this.log(LogLevel.DEBUG, message, ...args);
    }

    static info(message: string, ...args: any[]): void {
        this.log(LogLevel.INFO, message, ...args);
    }

    static warn(message: string, ...args: any[]): void {
        this.log(LogLevel.WARN, message, ...args);
    }

    static error(message: string, error?: any): void {
        this.log(LogLevel.ERROR, message, error);
    }

    private static log(level: LogLevel, message: string, ...args: any[]): void {
        if (level < this.logLevel) {
            return;
        }

        if (!this.outputChannel) {
            this.initialize();
        }

        const timestamp = new Date().toISOString();
        const levelName = LogLevel[level];
        
        let logMessage = `[${timestamp}] [${levelName}] ${message}`;
        
        if (args.length > 0) {
            logMessage += ' ' + args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
        }

        this.outputChannel.appendLine(logMessage);

        // Also log to console for development
        if (level >= LogLevel.ERROR) {
            console.error(logMessage);
        } else if (level >= LogLevel.WARN) {
            console.warn(logMessage);
        } else {
            console.log(logMessage);
        }
    }

    static show(): void {
        if (this.outputChannel) {
            this.outputChannel.show();
        }
    }

    static dispose(): void {
        if (this.outputChannel) {
            this.outputChannel.dispose();
        }
    }
}
