/**
 * Configuration management for the Agentic Code Assistant extension.
 */

import * as vscode from 'vscode';

export class ConfigurationManager {
    private static readonly SECTION = 'agentic';

    private config: vscode.WorkspaceConfiguration;

    constructor() {
        this.config = vscode.workspace.getConfiguration(ConfigurationManager.SECTION);
    }

    reload(): void {
        this.config = vscode.workspace.getConfiguration(ConfigurationManager.SECTION);
    }

    getApiUrl(): string {
        return this.config.get<string>('apiUrl', 'http://localhost:8000');
    }

    getDefaultLanguage(): string {
        return this.config.get<string>('defaultLanguage', 'python');
    }

    isAutoAnalyzeEnabled(): boolean {
        return this.config.get<boolean>('autoAnalyze', false);
    }

    isContextEnabled(): boolean {
        return this.config.get<boolean>('contextEnabled', true);
    }

    isTeamModeEnabled(): boolean {
        return this.config.get<boolean>('teamMode', false);
    }

    async updateApiUrl(url: string): Promise<void> {
        await this.config.update('apiUrl', url, vscode.ConfigurationTarget.Global);
    }

    async updateDefaultLanguage(language: string): Promise<void> {
        await this.config.update('defaultLanguage', language, vscode.ConfigurationTarget.Global);
    }

    async updateAutoAnalyze(enabled: boolean): Promise<void> {
        await this.config.update('autoAnalyze', enabled, vscode.ConfigurationTarget.Global);
    }

    async updateContextEnabled(enabled: boolean): Promise<void> {
        await this.config.update('contextEnabled', enabled, vscode.ConfigurationTarget.Global);
    }

    async updateTeamMode(enabled: boolean): Promise<void> {
        await this.config.update('teamMode', enabled, vscode.ConfigurationTarget.Global);
    }

    getAllSettings(): Record<string, any> {
        return {
            apiUrl: this.getApiUrl(),
            defaultLanguage: this.getDefaultLanguage(),
            autoAnalyze: this.isAutoAnalyzeEnabled(),
            contextEnabled: this.isContextEnabled(),
            teamMode: this.isTeamModeEnabled()
        };
    }
}
