/**
 * Main extension entry point for the Agentic Code Assistant VS Code extension.
 */

import * as vscode from 'vscode';
import { AgenticApiClient } from './api/client';
import { ChatProvider } from './providers/chatProvider';
import { ContextProvider } from './providers/contextProvider';
import { CodeGenerationProvider } from './providers/codeGenerationProvider';
import { CodeAnalysisProvider } from './providers/codeAnalysisProvider';
import { ConfigurationManager } from './utils/configuration';
import { Logger } from './utils/logger';

let apiClient: AgenticApiClient;
let chatProvider: ChatProvider;
let contextProvider: ContextProvider;
let codeGenerationProvider: CodeGenerationProvider;
let codeAnalysisProvider: CodeAnalysisProvider;

export function activate(context: vscode.ExtensionContext) {
    Logger.info('Activating Agentic Code Assistant extension');

    // Initialize configuration
    const config = new ConfigurationManager();
    
    // Initialize API client
    apiClient = new AgenticApiClient(config.getApiUrl());
    
    // Initialize providers
    chatProvider = new ChatProvider(context, apiClient);
    contextProvider = new ContextProvider(context, apiClient);
    codeGenerationProvider = new CodeGenerationProvider(apiClient);
    codeAnalysisProvider = new CodeAnalysisProvider(apiClient);
    
    // Register tree data providers
    vscode.window.registerTreeDataProvider('agentic.chatView', chatProvider);
    vscode.window.registerTreeDataProvider('agentic.contextView', contextProvider);
    
    // Register commands
    registerCommands(context);
    
    // Register event listeners
    registerEventListeners(context, config);
    
    // Initialize context tracking
    if (config.isContextEnabled()) {
        initializeContextTracking();
    }
    
    Logger.info('Agentic Code Assistant extension activated successfully');
}

function registerCommands(context: vscode.ExtensionContext) {
    // Generate Code command
    const generateCodeCommand = vscode.commands.registerCommand('agentic.generateCode', async () => {
        try {
            const prompt = await vscode.window.showInputBox({
                prompt: 'Describe the code you want to generate',
                placeHolder: 'e.g., Create a function to calculate fibonacci numbers'
            });
            
            if (prompt) {
                await codeGenerationProvider.generateCode(prompt);
            }
        } catch (error) {
            Logger.error('Code generation failed', error);
            vscode.window.showErrorMessage(`Code generation failed: ${error}`);
        }
    });
    
    // Analyze Code command
    const analyzeCodeCommand = vscode.commands.registerCommand('agentic.analyzeCode', async () => {
        try {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showWarningMessage('No active editor found');
                return;
            }
            
            const selection = editor.selection;
            const code = selection.isEmpty ? editor.document.getText() : editor.document.getText(selection);
            
            if (code.trim()) {
                await codeAnalysisProvider.analyzeCode(code, editor.document.languageId);
            } else {
                vscode.window.showWarningMessage('No code selected or found');
            }
        } catch (error) {
            Logger.error('Code analysis failed', error);
            vscode.window.showErrorMessage(`Code analysis failed: ${error}`);
        }
    });
    
    // Improve Code command
    const improveCodeCommand = vscode.commands.registerCommand('agentic.improveCode', async () => {
        try {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showWarningMessage('No active editor found');
                return;
            }
            
            const selection = editor.selection;
            const code = selection.isEmpty ? editor.document.getText() : editor.document.getText(selection);
            
            if (code.trim()) {
                const improvementType = await vscode.window.showQuickPick([
                    { label: 'General', value: 'general' },
                    { label: 'Performance', value: 'performance' },
                    { label: 'Security', value: 'security' },
                    { label: 'Style', value: 'style' }
                ], {
                    placeHolder: 'Select improvement type'
                });
                
                if (improvementType) {
                    await codeGenerationProvider.improveCode(code, editor.document.languageId, improvementType.value);
                }
            } else {
                vscode.window.showWarningMessage('No code selected or found');
            }
        } catch (error) {
            Logger.error('Code improvement failed', error);
            vscode.window.showErrorMessage(`Code improvement failed: ${error}`);
        }
    });
    
    // Generate Tests command
    const generateTestsCommand = vscode.commands.registerCommand('agentic.generateTests', async () => {
        try {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                vscode.window.showWarningMessage('No active editor found');
                return;
            }
            
            const code = editor.document.getText();
            if (code.trim()) {
                await codeGenerationProvider.generateTests(code, editor.document.languageId);
            } else {
                vscode.window.showWarningMessage('No code found in the current file');
            }
        } catch (error) {
            Logger.error('Test generation failed', error);
            vscode.window.showErrorMessage(`Test generation failed: ${error}`);
        }
    });
    
    // Open Chat command
    const openChatCommand = vscode.commands.registerCommand('agentic.openChat', async () => {
        try {
            await chatProvider.openChat();
        } catch (error) {
            Logger.error('Failed to open chat', error);
            vscode.window.showErrorMessage(`Failed to open chat: ${error}`);
        }
    });
    
    // Search Context command
    const searchContextCommand = vscode.commands.registerCommand('agentic.searchContext', async () => {
        try {
            const query = await vscode.window.showInputBox({
                prompt: 'Search context memory',
                placeHolder: 'Enter search query...'
            });
            
            if (query) {
                await contextProvider.searchContext(query);
            }
        } catch (error) {
            Logger.error('Context search failed', error);
            vscode.window.showErrorMessage(`Context search failed: ${error}`);
        }
    });
    
    // Register all commands
    context.subscriptions.push(
        generateCodeCommand,
        analyzeCodeCommand,
        improveCodeCommand,
        generateTestsCommand,
        openChatCommand,
        searchContextCommand
    );
}

function registerEventListeners(context: vscode.ExtensionContext, config: ConfigurationManager) {
    // Listen for file saves to auto-analyze if enabled
    const onDidSaveDocument = vscode.workspace.onDidSaveTextDocument(async (document) => {
        if (config.isAutoAnalyzeEnabled() && isSupportedLanguage(document.languageId)) {
            try {
                await codeAnalysisProvider.analyzeCode(document.getText(), document.languageId);
            } catch (error) {
                Logger.error('Auto-analysis failed', error);
            }
        }
    });
    
    // Listen for active editor changes to update context
    const onDidChangeActiveTextEditor = vscode.window.onDidChangeActiveTextEditor(async (editor) => {
        if (editor && config.isContextEnabled()) {
            try {
                await contextProvider.updateContext(editor.document);
            } catch (error) {
                Logger.error('Context update failed', error);
            }
        }
    });
    
    // Listen for configuration changes
    const onDidChangeConfiguration = vscode.workspace.onDidChangeConfiguration((event) => {
        if (event.affectsConfiguration('agentic')) {
            config.reload();
            apiClient.updateBaseUrl(config.getApiUrl());
            Logger.info('Configuration updated');
        }
    });
    
    context.subscriptions.push(
        onDidSaveDocument,
        onDidChangeActiveTextEditor,
        onDidChangeConfiguration
    );
}

async function initializeContextTracking() {
    try {
        // Add current workspace to context
        if (vscode.workspace.workspaceFolders) {
            for (const folder of vscode.workspace.workspaceFolders) {
                await contextProvider.addWorkspaceToContext(folder);
            }
        }
        
        // Add currently open files to context
        for (const document of vscode.workspace.textDocuments) {
            if (isSupportedLanguage(document.languageId)) {
                await contextProvider.updateContext(document);
            }
        }
        
        Logger.info('Context tracking initialized');
    } catch (error) {
        Logger.error('Failed to initialize context tracking', error);
    }
}

function isSupportedLanguage(languageId: string): boolean {
    const supportedLanguages = [
        'python', 'typescript', 'javascript', 'java', 'cpp', 'c',
        'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'scala',
        'csharp', 'html', 'css', 'sql', 'yaml', 'json', 'markdown'
    ];
    
    return supportedLanguages.includes(languageId);
}

export function deactivate() {
    Logger.info('Deactivating Agentic Code Assistant extension');
    
    // Cleanup resources
    if (apiClient) {
        apiClient.dispose();
    }
    
    Logger.info('Agentic Code Assistant extension deactivated');
}
