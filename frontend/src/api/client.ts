/**
 * API client for communicating with the Agentic backend system.
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Logger } from '../utils/logger';

export interface CodeGenerationRequest {
    prompt: string;
    language: string;
    context?: Record<string, any>;
    workflow_type?: string;
    include_tests?: boolean;
    include_analysis?: boolean;
}

export interface CodeAnalysisRequest {
    code: string;
    language: string;
    analysis_type?: string;
    context?: Record<string, any>;
}

export interface WorkflowResult {
    task_id: string;
    success: boolean;
    outputs: Record<string, any>;
    artifacts: Record<string, any>;
    execution_log: Array<Record<string, any>>;
    error?: string;
    metadata?: Record<string, any>;
}

export interface ContextSearchRequest {
    query: string;
    memory_types?: string[];
    limit?: number;
}

export interface MemoryItem {
    id: string;
    content: string;
    metadata: Record<string, any>;
    timestamp: string;
    access_count: number;
    importance: number;
    memory_type: string;
}

export class AgenticApiClient {
    private client: AxiosInstance;
    private baseUrl: string;

    constructor(baseUrl: string) {
        this.baseUrl = baseUrl;
        this.client = axios.create({
            baseURL: baseUrl,
            timeout: 60000, // 60 seconds
            headers: {
                'Content-Type': 'application/json',
            },
        });

        // Add request interceptor for logging
        this.client.interceptors.request.use(
            (config) => {
                Logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
                return config;
            },
            (error) => {
                Logger.error('API Request Error', error);
                return Promise.reject(error);
            }
        );

        // Add response interceptor for logging
        this.client.interceptors.response.use(
            (response) => {
                Logger.debug(`API Response: ${response.status} ${response.config.url}`);
                return response;
            },
            (error) => {
                Logger.error('API Response Error', error.response?.data || error.message);
                return Promise.reject(error);
            }
        );
    }

    updateBaseUrl(newBaseUrl: string): void {
        this.baseUrl = newBaseUrl;
        this.client.defaults.baseURL = newBaseUrl;
    }

    // Health check
    async healthCheck(): Promise<any> {
        const response = await this.client.get('/health');
        return response.data;
    }

    async detailedHealthCheck(): Promise<any> {
        const response = await this.client.get('/health/detailed');
        return response.data;
    }

    // Code generation
    async generateCode(request: CodeGenerationRequest): Promise<WorkflowResult> {
        const response = await this.client.post('/api/v1/agents/generate-code', request);
        return response.data;
    }

    // Code analysis
    async analyzeCode(request: CodeAnalysisRequest): Promise<WorkflowResult> {
        const response = await this.client.post('/api/v1/agents/analyze-code', request);
        return response.data;
    }

    // Code improvement
    async improveCode(code: string, language: string, improvementType: string): Promise<any> {
        const response = await this.client.post('/api/v1/code/improve', {
            code,
            language,
            improvement_type: improvementType,
        });
        return response.data;
    }

    // Simple code generation (without workflow)
    async generateSimpleCode(prompt: string, language: string, context?: string): Promise<any> {
        const response = await this.client.post('/api/v1/code/generate', {
            prompt,
            language,
            context,
        });
        return response.data;
    }

    // Simple code analysis (without workflow)
    async analyzeSimpleCode(code: string, language: string, analysisType: string = 'general'): Promise<any> {
        const response = await this.client.post('/api/v1/code/analyze', {
            content: code,
            language,
        }, {
            params: { analysis_type: analysisType }
        });
        return response.data;
    }

    // Context management
    async searchContext(request: ContextSearchRequest): Promise<MemoryItem[]> {
        const response = await this.client.post('/api/v1/context/search', request);
        return response.data;
    }

    async addToContext(content: string, metadata?: Record<string, any>): Promise<any> {
        const response = await this.client.post('/api/v1/context/add', {
            content,
            metadata,
        });
        return response.data;
    }

    async getContextSummary(): Promise<any> {
        const response = await this.client.get('/api/v1/context/summary');
        return response.data;
    }

    async getWorkingMemory(): Promise<MemoryItem[]> {
        const response = await this.client.get('/api/v1/context/working-memory');
        return response.data;
    }

    async getShortTermMemory(): Promise<MemoryItem[]> {
        const response = await this.client.get('/api/v1/context/short-term-memory');
        return response.data;
    }

    async clearMemory(memoryType: string): Promise<any> {
        const response = await this.client.delete(`/api/v1/context/clear/${memoryType}`);
        return response.data;
    }

    async getContextStats(): Promise<any> {
        const response = await this.client.get('/api/v1/context/stats');
        return response.data;
    }

    // Workflow management
    async getAvailableWorkflows(): Promise<string[]> {
        const response = await this.client.get('/api/v1/agents/workflows');
        return response.data;
    }

    async getWorkflowInfo(workflowType: string): Promise<any> {
        const response = await this.client.get(`/api/v1/agents/workflows/${workflowType}`);
        return response.data;
    }

    async getAgentCapabilities(): Promise<Record<string, string[]>> {
        const response = await this.client.get('/api/v1/agents/capabilities');
        return response.data;
    }

    async executeCustomWorkflow(request: any): Promise<WorkflowResult> {
        const response = await this.client.post('/api/v1/agents/custom-workflow', request);
        return response.data;
    }

    // File upload
    async uploadCodeFile(file: File, language?: string): Promise<any> {
        const formData = new FormData();
        formData.append('file', file);
        if (language) {
            formData.append('language', language);
        }

        const response = await this.client.post('/api/v1/code/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });
        return response.data;
    }

    // Utility methods
    async testConnection(): Promise<boolean> {
        try {
            await this.healthCheck();
            return true;
        } catch (error) {
            Logger.error('Connection test failed', error);
            return false;
        }
    }

    dispose(): void {
        // Cleanup any resources if needed
        Logger.info('API client disposed');
    }
}
