/**
 * Chat provider for the Agentic Code Assistant extension.
 */

import * as vscode from 'vscode';
import { AgenticApiClient } from '../api/client';
import { Logger } from '../utils/logger';

export class ChatProvider implements vscode.TreeDataProvider<ChatItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<ChatItem | undefined | null | void> = new vscode.EventEmitter<ChatItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<ChatItem | undefined | null | void> = this._onDidChangeTreeData.event;

    private chatHistory: ChatMessage[] = [];
    private webviewPanel: vscode.WebviewPanel | undefined;

    constructor(
        private context: vscode.ExtensionContext,
        private apiClient: AgenticApiClient
    ) {}

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: ChatItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: ChatItem): Thenable<ChatItem[]> {
        if (!element) {
            // Root level - show recent conversations
            return Promise.resolve([
                new ChatItem('New Chat', vscode.TreeItemCollapsibleState.None, 'new-chat'),
                new ChatItem('Recent Conversations', vscode.TreeItemCollapsibleState.Expanded, 'recent'),
            ]);
        } else if (element.contextValue === 'recent') {
            // Show recent chat items
            return Promise.resolve(
                this.chatHistory.slice(-5).map((msg, index) => 
                    new ChatItem(
                        `${msg.role}: ${msg.content.substring(0, 50)}...`,
                        vscode.TreeItemCollapsibleState.None,
                        'chat-message'
                    )
                )
            );
        }
        return Promise.resolve([]);
    }

    async openChat(): Promise<void> {
        if (this.webviewPanel) {
            this.webviewPanel.reveal();
            return;
        }

        this.webviewPanel = vscode.window.createWebviewPanel(
            'agenticChat',
            'Agentic Assistant',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.webviewPanel.webview.html = this.getChatWebviewContent();

        // Handle messages from the webview
        this.webviewPanel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'sendMessage':
                        await this.handleChatMessage(message.text);
                        break;
                    case 'clearChat':
                        this.clearChat();
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        this.webviewPanel.onDidDispose(() => {
            this.webviewPanel = undefined;
        });
    }

    private async handleChatMessage(userMessage: string): Promise<void> {
        try {
            // Add user message to history
            this.addMessage('user', userMessage);

            // Get current context
            const editor = vscode.window.activeTextEditor;
            const context = editor ? {
                file_path: editor.document.fileName,
                language: editor.document.languageId,
                selected_text: editor.document.getText(editor.selection),
                current_content: editor.document.getText()
            } : {};

            // Send to backend for processing
            const response = await this.apiClient.executeCustomWorkflow({
                task_type: 'chat_assistance',
                prompt: userMessage,
                context,
                workflow_type: 'code_generation'
            });

            if (response.success && response.outputs) {
                const assistantMessage = response.outputs.response || response.outputs.code || 'I understand. How can I help you further?';
                this.addMessage('assistant', assistantMessage);
            } else {
                this.addMessage('assistant', 'I apologize, but I encountered an error processing your request.');
            }

        } catch (error) {
            Logger.error('Chat message handling failed', error);
            this.addMessage('assistant', 'I apologize, but I encountered an error. Please try again.');
        }

        this.updateChatWebview();
        this.refresh();
    }

    private addMessage(role: 'user' | 'assistant', content: string): void {
        this.chatHistory.push({
            role,
            content,
            timestamp: new Date()
        });

        // Keep only last 50 messages
        if (this.chatHistory.length > 50) {
            this.chatHistory = this.chatHistory.slice(-50);
        }
    }

    private clearChat(): void {
        this.chatHistory = [];
        this.updateChatWebview();
        this.refresh();
    }

    private updateChatWebview(): void {
        if (this.webviewPanel) {
            this.webviewPanel.webview.postMessage({
                command: 'updateChat',
                messages: this.chatHistory
            });
        }
    }

    private getChatWebviewContent(): string {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Agentic Assistant</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 0;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }
                .chat-container {
                    flex: 1;
                    overflow-y: auto;
                    padding: 16px;
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                }
                .message {
                    max-width: 80%;
                    padding: 12px 16px;
                    border-radius: 8px;
                    word-wrap: break-word;
                }
                .user-message {
                    align-self: flex-end;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                }
                .assistant-message {
                    align-self: flex-start;
                    background-color: var(--vscode-textBlockQuote-background);
                    border-left: 4px solid var(--vscode-textBlockQuote-border);
                }
                .input-container {
                    padding: 16px;
                    border-top: 1px solid var(--vscode-panel-border);
                    display: flex;
                    gap: 8px;
                }
                .message-input {
                    flex: 1;
                    padding: 8px 12px;
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border: 1px solid var(--vscode-input-border);
                    border-radius: 4px;
                    outline: none;
                }
                .send-button, .clear-button {
                    padding: 8px 16px;
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                }
                .send-button:hover, .clear-button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }
                .timestamp {
                    font-size: 0.8em;
                    opacity: 0.7;
                    margin-top: 4px;
                }
                pre {
                    background-color: var(--vscode-textPreformat-background);
                    padding: 8px;
                    border-radius: 4px;
                    overflow-x: auto;
                }
                code {
                    background-color: var(--vscode-textPreformat-background);
                    padding: 2px 4px;
                    border-radius: 2px;
                }
            </style>
        </head>
        <body>
            <div class="chat-container" id="chatContainer">
                <div class="assistant-message">
                    <div>Hello! I'm your Agentic Code Assistant. I can help you with:</div>
                    <ul>
                        <li>Generating code from descriptions</li>
                        <li>Analyzing and improving existing code</li>
                        <li>Creating tests and documentation</li>
                        <li>Answering questions about your codebase</li>
                        <li>Providing coding best practices and suggestions</li>
                    </ul>
                    <div>How can I assist you today?</div>
                </div>
            </div>
            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" placeholder="Ask me anything about your code..." />
                <button id="sendButton" class="send-button">Send</button>
                <button id="clearButton" class="clear-button">Clear</button>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                const chatContainer = document.getElementById('chatContainer');
                const messageInput = document.getElementById('messageInput');
                const sendButton = document.getElementById('sendButton');
                const clearButton = document.getElementById('clearButton');

                function sendMessage() {
                    const text = messageInput.value.trim();
                    if (text) {
                        vscode.postMessage({
                            command: 'sendMessage',
                            text: text
                        });
                        messageInput.value = '';
                    }
                }

                function clearChat() {
                    vscode.postMessage({
                        command: 'clearChat'
                    });
                }

                function addMessage(role, content, timestamp) {
                    const messageDiv = document.createElement('div');
                    messageDiv.className = \`message \${role}-message\`;
                    
                    const contentDiv = document.createElement('div');
                    contentDiv.innerHTML = formatMessage(content);
                    messageDiv.appendChild(contentDiv);
                    
                    if (timestamp) {
                        const timestampDiv = document.createElement('div');
                        timestampDiv.className = 'timestamp';
                        timestampDiv.textContent = new Date(timestamp).toLocaleTimeString();
                        messageDiv.appendChild(timestampDiv);
                    }
                    
                    chatContainer.appendChild(messageDiv);
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }

                function formatMessage(content) {
                    // Simple markdown-like formatting
                    return content
                        .replace(/\`\`\`([\\s\\S]*?)\`\`\`/g, '<pre><code>$1</code></pre>')
                        .replace(/\`([^\`]+)\`/g, '<code>$1</code>')
                        .replace(/\\n/g, '<br>');
                }

                sendButton.addEventListener('click', sendMessage);
                clearButton.addEventListener('click', clearChat);
                
                messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });

                // Handle messages from the extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.command) {
                        case 'updateChat':
                            // Clear existing messages except welcome
                            const messages = chatContainer.querySelectorAll('.message:not(:first-child)');
                            messages.forEach(msg => msg.remove());
                            
                            // Add all messages
                            message.messages.forEach(msg => {
                                addMessage(msg.role, msg.content, msg.timestamp);
                            });
                            break;
                    }
                });

                // Focus input on load
                messageInput.focus();
            </script>
        </body>
        </html>
        `;
    }
}

class ChatItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly contextValue: string
    ) {
        super(label, collapsibleState);
        this.tooltip = this.label;
    }
}

interface ChatMessage {
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
}
