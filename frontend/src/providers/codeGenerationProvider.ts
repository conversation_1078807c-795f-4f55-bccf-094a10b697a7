/**
 * Code generation provider for the Agentic Code Assistant extension.
 */

import * as vscode from 'vscode';
import { AgenticApiClient, CodeGenerationRequest } from '../api/client';
import { Logger } from '../utils/logger';

export class CodeGenerationProvider {
    constructor(private apiClient: AgenticApiClient) {}

    async generateCode(prompt: string): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        const language = editor.document.languageId;
        
        // Show progress
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Generating code...',
            cancellable: false
        }, async (progress) => {
            try {
                progress.report({ increment: 20, message: 'Preparing request...' });

                const request: CodeGenerationRequest = {
                    prompt,
                    language,
                    context: {
                        file_path: editor.document.fileName,
                        current_content: editor.document.getText()
                    },
                    workflow_type: 'code_generation',
                    include_analysis: true
                };

                progress.report({ increment: 30, message: 'Sending to AI agents...' });

                const result = await this.apiClient.generateCode(request);

                progress.report({ increment: 40, message: 'Processing response...' });

                if (result.success && result.outputs.code) {
                    // Insert generated code at cursor position
                    const position = editor.selection.active;
                    await editor.edit(editBuilder => {
                        editBuilder.insert(position, result.outputs.code);
                    });

                    progress.report({ increment: 10, message: 'Code inserted successfully' });

                    // Show analysis if available
                    if (result.outputs.analysis) {
                        this.showAnalysisResult(result.outputs.analysis);
                    }

                    vscode.window.showInformationMessage('Code generated successfully!');
                } else {
                    throw new Error(result.error || 'Code generation failed');
                }
            } catch (error) {
                Logger.error('Code generation failed', error);
                vscode.window.showErrorMessage(`Code generation failed: ${error}`);
            }
        });
    }

    async improveCode(code: string, language: string, improvementType: string): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Improving code...',
            cancellable: false
        }, async (progress) => {
            try {
                progress.report({ increment: 25, message: 'Analyzing current code...' });

                const improvedCode = await this.apiClient.improveCode(code, language, improvementType);

                progress.report({ increment: 50, message: 'Generating improvements...' });

                if (improvedCode.content) {
                    // Show diff and ask user to confirm
                    const action = await vscode.window.showInformationMessage(
                        'Code improvement ready. Would you like to replace the current code?',
                        'Replace', 'Show Diff', 'Cancel'
                    );

                    if (action === 'Replace') {
                        const selection = editor.selection;
                        await editor.edit(editBuilder => {
                            if (selection.isEmpty) {
                                editBuilder.replace(new vscode.Range(0, 0, editor.document.lineCount, 0), improvedCode.content);
                            } else {
                                editBuilder.replace(selection, improvedCode.content);
                            }
                        });
                        vscode.window.showInformationMessage('Code improved successfully!');
                    } else if (action === 'Show Diff') {
                        await this.showCodeDiff(code, improvedCode.content, 'Code Improvement');
                    }
                }

                progress.report({ increment: 25, message: 'Complete' });
            } catch (error) {
                Logger.error('Code improvement failed', error);
                vscode.window.showErrorMessage(`Code improvement failed: ${error}`);
            }
        });
    }

    async generateTests(code: string, language: string): Promise<void> {
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Generating tests...',
            cancellable: false
        }, async (progress) => {
            try {
                progress.report({ increment: 20, message: 'Analyzing code structure...' });

                const request: CodeGenerationRequest = {
                    prompt: 'Generate comprehensive tests for this code',
                    language,
                    context: {
                        code,
                        task_type: 'test_generation'
                    },
                    workflow_type: 'full_development',
                    include_tests: true
                };

                progress.report({ increment: 40, message: 'Generating test cases...' });

                const result = await this.apiClient.generateCode(request);

                progress.report({ increment: 30, message: 'Formatting tests...' });

                if (result.success && result.outputs.tests) {
                    // Create new test file
                    const testFileName = this.generateTestFileName(vscode.window.activeTextEditor?.document.fileName || 'code', language);
                    const testUri = vscode.Uri.file(testFileName);
                    
                    const testDocument = await vscode.workspace.openTextDocument(testUri.with({ scheme: 'untitled' }));
                    const testEditor = await vscode.window.showTextDocument(testDocument);
                    
                    await testEditor.edit(editBuilder => {
                        editBuilder.insert(new vscode.Position(0, 0), result.outputs.tests);
                    });

                    progress.report({ increment: 10, message: 'Tests generated successfully' });
                    vscode.window.showInformationMessage('Tests generated successfully!');
                } else {
                    throw new Error(result.error || 'Test generation failed');
                }
            } catch (error) {
                Logger.error('Test generation failed', error);
                vscode.window.showErrorMessage(`Test generation failed: ${error}`);
            }
        });
    }

    private async showCodeDiff(original: string, improved: string, title: string): Promise<void> {
        const originalUri = vscode.Uri.parse(`untitled:Original`);
        const improvedUri = vscode.Uri.parse(`untitled:Improved`);

        const originalDoc = await vscode.workspace.openTextDocument(originalUri.with({ 
            scheme: 'untitled',
            path: 'original.tmp'
        }));
        
        const improvedDoc = await vscode.workspace.openTextDocument(improvedUri.with({ 
            scheme: 'untitled',
            path: 'improved.tmp'
        }));

        // This is a simplified diff view - in a real implementation,
        // you'd use VS Code's diff editor API
        await vscode.commands.executeCommand('vscode.diff', originalUri, improvedUri, title);
    }

    private generateTestFileName(originalFileName: string, language: string): string {
        const baseName = originalFileName.replace(/\.[^/.]+$/, '');
        
        const testExtensions: Record<string, string> = {
            'python': '.test.py',
            'javascript': '.test.js',
            'typescript': '.test.ts',
            'java': 'Test.java',
            'go': '_test.go',
            'rust': '_test.rs'
        };

        const extension = testExtensions[language] || '.test.txt';
        return `${baseName}${extension}`;
    }

    private showAnalysisResult(analysis: string): void {
        const panel = vscode.window.createWebviewPanel(
            'agenticAnalysis',
            'Code Analysis',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true
            }
        );

        panel.webview.html = this.getAnalysisWebviewContent(analysis);
    }

    private getAnalysisWebviewContent(analysis: string): string {
        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Code Analysis</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                    line-height: 1.6;
                }
                .analysis-content {
                    white-space: pre-wrap;
                    background-color: var(--vscode-textBlockQuote-background);
                    border-left: 4px solid var(--vscode-textBlockQuote-border);
                    padding: 16px;
                    margin: 16px 0;
                }
                h1 {
                    color: var(--vscode-textPreformat-foreground);
                    border-bottom: 1px solid var(--vscode-textSeparator-foreground);
                    padding-bottom: 8px;
                }
            </style>
        </head>
        <body>
            <h1>Code Analysis Results</h1>
            <div class="analysis-content">${analysis}</div>
        </body>
        </html>
        `;
    }
}
