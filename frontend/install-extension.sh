#!/bin/bash

# Install script for the Agentic Code Assistant VS Code Extension

echo "🤖 Installing Agentic Code Assistant VS Code Extension..."

# Check if VS Code is installed
if ! command -v code &> /dev/null; then
    echo "❌ VS Code 'code' command not found."
    echo "Please install VS Code and add it to your PATH, or install manually:"
    echo "1. Open VS Code"
    echo "2. Press Ctrl+Shift+P (Cmd+Shift+P on Mac)"
    echo "3. Type 'Extensions: Install from VSIX'"
    echo "4. Select the agentic-code-assistant-1.0.0.vsix file"
    exit 1
fi

# Check if VSIX file exists
if [ ! -f "agentic-code-assistant-1.0.0.vsix" ]; then
    echo "❌ VSIX file not found. Please run 'npm run package' first."
    exit 1
fi

# Install the extension
echo "📦 Installing extension from VSIX..."
code --install-extension agentic-code-assistant-1.0.0.vsix

if [ $? -eq 0 ]; then
    echo "✅ Extension installed successfully!"
    echo ""
    echo "🚀 Next steps:"
    echo "1. Make sure the backend server is running at http://localhost:8000"
    echo "2. Open a code file in VS Code"
    echo "3. Try the following commands:"
    echo "   - Ctrl+Alt+G (Cmd+Alt+G): Generate code"
    echo "   - Ctrl+Alt+A (Cmd+Alt+A): Analyze code"
    echo "   - Ctrl+Alt+C (Cmd+Alt+C): Open chat"
    echo ""
    echo "📖 Open the test-workspace folder to try the extension!"
else
    echo "❌ Failed to install extension."
    echo "Please install manually through VS Code."
fi
