# Agentic Code Generation System Environment Configuration

# Application Settings
DEBUG=true
HOST=0.0.0.0
PORT=8000
ALLOWED_ORIGINS=["http://localhost:3000", "vscode-webview://*"]

# Security
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# LLM Configuration
DEFAULT_LLM_PROVIDER=openai
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
MAX_TOKENS=4000
TEMPERATURE=0.1

# Vector Database (ChromaDB)
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME=agentic_code
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Graph Database (Neo4j)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Redis Cache
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Context Engine
MAX_CONTEXT_LENGTH=16000
WORKING_MEMORY_SIZE=10
SHORT_TERM_MEMORY_SIZE=100
MEMORY_CONSOLIDATION_THRESHOLD=0.8

# Agent Configuration
MAX_AGENT_ITERATIONS=10
AGENT_TIMEOUT=300

# Code Analysis
MAX_FILE_SIZE=1048576
SUPPORTED_LANGUAGES=["python", "typescript", "javascript", "java", "cpp", "c", "go", "rust", "php", "ruby", "swift", "kotlin", "scala", "csharp", "html", "css", "sql", "yaml", "json", "markdown"]

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=60
AUTO_RELOAD=false
