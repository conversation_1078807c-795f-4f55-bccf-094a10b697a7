# Getting Started with Agentic Code Generation System

## Prerequisites

Before you begin, ensure you have the following installed:

- **Python 3.11+**: For the backend system
- **Node.js 18+**: For the VS Code extension
- **VS Code**: For the frontend interface
- **Docker** (optional): For containerized deployment
- **Git**: For version control

## Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd agentic
```

### 2. Set Up Environment Variables

```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# LLM API Keys (at least one required)
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Database Configuration (optional - will use defaults)
CHROMA_HOST=localhost
CHROMA_PORT=8001
NEO4J_URI=bolt://localhost:7687
NEO4J_PASSWORD=your-neo4j-password
REDIS_URL=redis://localhost:6379
```

### 3. Set Up Backend

```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Start the backend server
python -m uvicorn src.main:app --reload
```

The backend will be available at `http://localhost:8000`

### 4. Set Up Frontend (VS Code Extension)

```bash
cd frontend

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# For development with auto-reload
npm run watch
```

### 5. Install the Extension

1. Open VS Code
2. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
3. Type "Extensions: Install from VSIX"
4. Navigate to the `frontend` directory and select the generated `.vsix` file

Or for development:

1. Open the `frontend` folder in VS Code
2. Press `F5` to launch a new Extension Development Host window

## Basic Usage

### 1. Generate Code

1. Open a file in VS Code
2. Press `Ctrl+Alt+G` (or `Cmd+Alt+G` on Mac)
3. Enter a description of the code you want to generate
4. The AI will generate and insert the code at your cursor position

### 2. Analyze Code

1. Select code in the editor (or place cursor in a function/class)
2. Press `Ctrl+Alt+A` (or `Cmd+Alt+A` on Mac)
3. View the analysis results in the side panel

### 3. Improve Code

1. Select the code you want to improve
2. Right-click and select "Agentic: Improve Code"
3. Choose the improvement type (general, performance, security, style)
4. Review and apply the suggested improvements

### 4. Generate Tests

1. Open a file with code you want to test
2. Right-click and select "Agentic: Generate Tests"
3. The AI will create comprehensive tests in a new file

### 5. Use the Chat Interface

1. Press `Ctrl+Alt+C` (or `Cmd+Alt+C` on Mac)
2. Ask questions about your code or request assistance
3. The AI will provide contextual help based on your current project

## Configuration

### Backend Configuration

The backend can be configured through environment variables or the `.env` file:

```env
# Application Settings
DEBUG=true
HOST=0.0.0.0
PORT=8000

# LLM Configuration
DEFAULT_LLM_PROVIDER=openai
MAX_TOKENS=4000
TEMPERATURE=0.1

# Context Engine
WORKING_MEMORY_SIZE=10
SHORT_TERM_MEMORY_SIZE=100
MAX_CONTEXT_LENGTH=16000

# Performance
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=60
```

### Extension Configuration

Configure the VS Code extension through VS Code settings:

1. Open VS Code settings (`Ctrl+,` or `Cmd+,`)
2. Search for "Agentic"
3. Configure the following options:

- **API URL**: Backend server URL (default: `http://localhost:8000`)
- **Default Language**: Default programming language for generation
- **Auto Analyze**: Automatically analyze code on file save
- **Context Enabled**: Enable context tracking and memory
- **Team Mode**: Enable team collaboration features

## Database Setup (Optional)

For full functionality, you may want to set up the databases:

### ChromaDB (Vector Database)

```bash
# Install ChromaDB
pip install chromadb

# Run ChromaDB server
chroma run --host localhost --port 8001
```

### Neo4j (Graph Database)

```bash
# Using Docker
docker run \
    --name neo4j \
    -p 7474:7474 -p 7687:7687 \
    -d \
    -v $HOME/neo4j/data:/data \
    -v $HOME/neo4j/logs:/logs \
    -v $HOME/neo4j/import:/var/lib/neo4j/import \
    -v $HOME/neo4j/plugins:/plugins \
    --env NEO4J_AUTH=neo4j/password \
    neo4j:latest
```

### Redis (Cache)

```bash
# Using Docker
docker run --name redis -p 6379:6379 -d redis:latest

# Or install locally
# On macOS: brew install redis
# On Ubuntu: sudo apt-get install redis-server
```

## Verification

### 1. Check Backend Health

```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "components": {}
}
```

### 2. Test API Endpoints

```bash
# Test code generation
curl -X POST http://localhost:8000/api/v1/code/generate \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Create a hello world function", "language": "python"}'

# Test context search
curl -X POST http://localhost:8000/api/v1/context/search \
  -H "Content-Type: application/json" \
  -d '{"query": "hello world", "limit": 5}'
```

### 3. Verify Extension

1. Open VS Code with the extension installed
2. Open the Command Palette (`Ctrl+Shift+P`)
3. Type "Agentic" - you should see the available commands
4. Try generating some code to verify the connection

## Troubleshooting

### Common Issues

1. **Backend won't start**
   - Check Python version (3.11+ required)
   - Verify all dependencies are installed
   - Check for port conflicts (8000)

2. **Extension not working**
   - Verify backend is running
   - Check API URL in extension settings
   - Look at VS Code Developer Console for errors

3. **LLM requests failing**
   - Verify API keys are set correctly
   - Check internet connection
   - Verify API key permissions and quotas

4. **Database connection errors**
   - Databases are optional for basic functionality
   - Check database server status
   - Verify connection strings in `.env`

### Logs and Debugging

- **Backend logs**: Check console output where you started the server
- **Extension logs**: Open VS Code Developer Console (`Help > Toggle Developer Tools`)
- **Detailed health check**: Visit `http://localhost:8000/health/detailed`

## Next Steps

1. **Explore Features**: Try all the available commands and features
2. **Customize Configuration**: Adjust settings to match your workflow
3. **Read Documentation**: Check out the other documentation files
4. **Join Community**: Connect with other users and contributors
5. **Contribute**: Help improve the system by reporting issues or contributing code

## Support

- **Documentation**: Check the `docs/` directory for detailed guides
- **Issues**: Report bugs and feature requests on GitHub
- **Community**: Join our Discord/Slack for discussions
- **Email**: Contact <NAME_EMAIL>
