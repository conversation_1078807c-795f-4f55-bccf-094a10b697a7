# Agentic Code Generation System

A powerful AI-driven code generation and analysis system that combines multiple specialized agents to provide intelligent coding assistance, similar to Windsurf and Cursor AI.

## 🚀 Features

- **Multi-Language Support**: Python, TypeScript, JavaScript, Java, C++, Go, Rust, and more
- **Intelligent Code Generation**: Production-grade code from natural language descriptions
- **Advanced Code Analysis**: Deep understanding of codebases and relationships
- **Brain-like Context Engine**: Hybrid memory system for maintaining project context
- **Agent Orchestration**: Specialized AI agents for different coding tasks
- **VS Code Integration**: Seamless development experience
- **Team Collaboration**: Real-time collaboration features
- **Multi-LLM Support**: Works with OpenAI, Claude, local models via LiteLLM

## 🏗️ Architecture

### Backend (Python)
- **FastAPI**: High-performance API server
- **LangGraph**: Agent orchestration and workflow management
- **LiteLLM**: Multi-provider LLM integration
- **ChromaDB + Neo4j**: Hybrid context engine

### Frontend (TypeScript)
- **VS Code Extension**: Primary interface
- **Real-time Communication**: WebSocket-based updates
- **Collaborative Features**: Team synchronization

### Context Engine
- **Vector Database**: Semantic code search and similarity
- **Graph Database**: Code relationships and dependencies
- **Memory Patterns**: Working, short-term, and long-term memory
- **Context Consolidation**: Intelligent information synthesis

## 🛠️ Tech Stack

- **Backend**: Python 3.11+, FastAPI, LangGraph, LiteLLM
- **Database**: ChromaDB (vector), Neo4j (graph), Redis (cache)
- **Frontend**: TypeScript, VS Code Extension API
- **AI/ML**: Multiple LLM providers, embedding models
- **DevOps**: Docker, pytest, pre-commit hooks

## 📁 Project Structure

```
agentic/
├── backend/                 # Python backend system
│   ├── src/
│   │   ├── agents/         # Specialized AI agents
│   │   ├── context/        # Context engine and memory
│   │   ├── llm/           # LLM integration and management
│   │   ├── api/           # FastAPI routes and endpoints
│   │   └── core/          # Core utilities and config
│   ├── tests/             # Backend tests
│   └── requirements.txt   # Python dependencies
├── frontend/              # VS Code extension
│   ├── src/              # TypeScript source
│   ├── package.json      # Extension manifest
│   └── webpack.config.js # Build configuration
├── docs/                 # Documentation
├── docker/              # Docker configurations
└── scripts/             # Development scripts
```

## 🚦 Getting Started

### Prerequisites
- Python 3.11+
- Node.js 18+
- Docker (optional)
- VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd agentic
   ```

2. **Set up backend**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Set up frontend**
   ```bash
   cd frontend
   npm install
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

5. **Start the system**
   ```bash
   # Backend
   cd backend && python -m uvicorn src.main:app --reload

   # Frontend (VS Code extension development)
   cd frontend && npm run dev
   ```

## 🤖 Agent System

The system includes specialized agents for different coding tasks:

- **Code Generator**: Creates new code from specifications
- **Code Analyzer**: Analyzes existing codebases
- **Refactoring Agent**: Suggests and implements improvements
- **Test Generator**: Creates comprehensive test suites
- **Documentation Agent**: Generates and maintains documentation
- **Bug Hunter**: Detects and fixes issues
- **Performance Optimizer**: Optimizes code performance

## 🧠 Context Engine

The brain-like memory system maintains:

- **Working Memory**: Current task context and immediate variables
- **Short-term Memory**: Recent interactions and temporary context
- **Long-term Memory**: Project knowledge and learned patterns
- **Semantic Memory**: Code concepts and relationships
- **Episodic Memory**: Development history and decisions

## 🔧 Configuration

Key configuration options:

- **LLM Providers**: Configure multiple LLM providers
- **Context Settings**: Adjust memory and context parameters
- **Agent Behavior**: Customize agent personalities and capabilities
- **Collaboration**: Set up team features and permissions

## 📚 Documentation

- [Architecture Guide](docs/architecture.md)
- [API Reference](docs/api.md)
- [Agent Development](docs/agents.md)
- [Context Engine](docs/context.md)
- [VS Code Extension](docs/extension.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- LangGraph for agent orchestration
- LiteLLM for multi-provider LLM support
- The open-source AI community
