"""
Context-related API endpoints for the Agentic Code Generation System.
"""

from typing import Any, Dict, List, Optional

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from pydantic import BaseModel, Field

from src.context.engine import ContextEngine, MemoryItem
from src.core.logging import get_logger
from src.main import app

router = APIRouter()
logger = get_logger(__name__)


class MemoryItemResponse(BaseModel):
    """Response model for memory items."""
    id: str
    content: str
    metadata: Dict[str, Any]
    timestamp: str
    access_count: int
    importance: float
    memory_type: str


class ContextSearchRequest(BaseModel):
    """Request for context search."""
    query: str
    memory_types: Optional[List[str]] = None
    limit: int = Field(default=10, ge=1, le=50)


class ContextAddRequest(BaseModel):
    """Request to add content to context."""
    content: str
    metadata: Optional[Dict[str, Any]] = None


class ContextSummaryResponse(BaseModel):
    """Response for context summary."""
    working_memory: Dict[str, Any]
    short_term_memory: Dict[str, Any]
    long_term_memory: Dict[str, Any]
    graph_database: Dict[str, Any]


def get_context_engine() -> ContextEngine:
    """Get context engine from app state."""
    if not hasattr(app.state, 'context_engine'):
        raise HTTPException(status_code=503, detail="Context Engine not initialized")
    return app.state.context_engine


@router.post("/search", response_model=List[MemoryItemResponse])
async def search_context(
    request: ContextSearchRequest,
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Search across all memory types."""
    try:
        logger.info(
            "Context search request",
            query=request.query[:50] + "..." if len(request.query) > 50 else request.query,
            memory_types=request.memory_types,
            limit=request.limit
        )
        
        results = await context_engine.search_memory(
            query=request.query,
            memory_types=request.memory_types,
            limit=request.limit
        )
        
        return [
            MemoryItemResponse(
                id=item.id,
                content=item.content,
                metadata=item.metadata,
                timestamp=item.timestamp.isoformat(),
                access_count=item.access_count,
                importance=item.importance,
                memory_type=item.memory_type
            )
            for item in results
        ]
        
    except Exception as e:
        logger.error("Context search failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Context search failed: {str(e)}")


@router.post("/add", response_model=Dict[str, str])
async def add_to_context(
    request: ContextAddRequest,
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Add content to working memory."""
    try:
        logger.info(
            "Adding content to context",
            content_length=len(request.content),
            has_metadata=bool(request.metadata)
        )
        
        item_id = await context_engine.add_to_working_memory(
            content=request.content,
            metadata=request.metadata
        )
        
        return {
            "message": "Content added to context successfully",
            "item_id": item_id
        }
        
    except Exception as e:
        logger.error("Failed to add content to context", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to add content: {str(e)}")


@router.get("/summary", response_model=ContextSummaryResponse)
async def get_context_summary(
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Get a summary of the current context state."""
    try:
        summary = await context_engine.get_context_summary()
        
        return ContextSummaryResponse(
            working_memory=summary["working_memory"],
            short_term_memory=summary["short_term_memory"],
            long_term_memory=summary["long_term_memory"],
            graph_database=summary["graph_database"]
        )
        
    except Exception as e:
        logger.error("Failed to get context summary", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get summary: {str(e)}")


@router.get("/working-memory", response_model=List[MemoryItemResponse])
async def get_working_memory(
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Get current working memory contents."""
    try:
        working_memory = context_engine.working_memory
        
        return [
            MemoryItemResponse(
                id=item.id,
                content=item.content,
                metadata=item.metadata,
                timestamp=item.timestamp.isoformat(),
                access_count=item.access_count,
                importance=item.importance,
                memory_type=item.memory_type
            )
            for item in working_memory
        ]
        
    except Exception as e:
        logger.error("Failed to get working memory", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get working memory: {str(e)}")


@router.get("/short-term-memory", response_model=List[MemoryItemResponse])
async def get_short_term_memory(
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Get current short-term memory contents."""
    try:
        short_term_memory = context_engine.short_term_memory
        
        return [
            MemoryItemResponse(
                id=item.id,
                content=item.content,
                metadata=item.metadata,
                timestamp=item.timestamp.isoformat(),
                access_count=item.access_count,
                importance=item.importance,
                memory_type=item.memory_type
            )
            for item in short_term_memory
        ]
        
    except Exception as e:
        logger.error("Failed to get short-term memory", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get short-term memory: {str(e)}")


@router.delete("/clear/{memory_type}")
async def clear_memory(
    memory_type: str,
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Clear specific memory type."""
    try:
        if memory_type == "working":
            context_engine.working_memory.clear()
        elif memory_type == "short_term":
            context_engine.short_term_memory.clear()
        elif memory_type == "all":
            context_engine.working_memory.clear()
            context_engine.short_term_memory.clear()
        else:
            raise HTTPException(status_code=400, detail=f"Invalid memory type: {memory_type}")
        
        logger.info("Memory cleared", memory_type=memory_type)
        
        return {"message": f"{memory_type} memory cleared successfully"}
        
    except Exception as e:
        logger.error("Failed to clear memory", error=str(e), memory_type=memory_type)
        raise HTTPException(status_code=500, detail=f"Failed to clear memory: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_context_stats(
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Get context engine statistics."""
    try:
        stats = {
            "working_memory": {
                "count": len(context_engine.working_memory),
                "limit": context_engine.working_memory_limit,
                "utilization": len(context_engine.working_memory) / context_engine.working_memory_limit
            },
            "short_term_memory": {
                "count": len(context_engine.short_term_memory),
                "limit": context_engine.short_term_memory_limit,
                "utilization": len(context_engine.short_term_memory) / context_engine.short_term_memory_limit
            },
            "databases": {
                "vector_db_available": context_engine.chroma_collection is not None,
                "graph_db_available": context_engine.neo4j_driver is not None
            }
        }
        
        return stats
        
    except Exception as e:
        logger.error("Failed to get context stats", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get stats: {str(e)}")


@router.post("/consolidate")
async def consolidate_memory(
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Manually trigger memory consolidation."""
    try:
        # Move some short-term memory to long-term
        if context_engine.short_term_memory:
            items_to_consolidate = context_engine.short_term_memory[:5]  # Consolidate 5 items
            context_engine.short_term_memory = context_engine.short_term_memory[5:]
            
            await context_engine._consolidate_to_long_term_memory(items_to_consolidate)
            
            logger.info("Memory consolidation triggered", items_consolidated=len(items_to_consolidate))
            
            return {
                "message": "Memory consolidation completed",
                "items_consolidated": len(items_to_consolidate)
            }
        else:
            return {"message": "No items to consolidate"}
        
    except Exception as e:
        logger.error("Memory consolidation failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Consolidation failed: {str(e)}")
