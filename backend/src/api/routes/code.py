"""
Code-related API endpoints for the Agentic Code Generation System.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from pydantic import BaseModel, Field

from src.context.engine import ContextEngine, CodeContext
from src.core.logging import get_logger
from src.llm.manager import LLMManager
from src.main import app

router = APIRouter()
logger = get_logger(__name__)


class CodeSnippet(BaseModel):
    """Code snippet model."""
    content: str
    language: str
    file_path: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class CodeAnalysisResult(BaseModel):
    """Code analysis result model."""
    analysis: str
    suggestions: List[str]
    issues: List[Dict[str, Any]]
    metrics: Dict[str, Any]


class CodeGenerationRequest(BaseModel):
    """Simple code generation request."""
    prompt: str
    language: str = "python"
    context: Optional[str] = None


class CodeImprovementRequest(BaseModel):
    """Code improvement request."""
    code: str
    language: str
    improvement_type: str = "general"  # general, performance, security, style


def get_llm_manager() -> LLMManager:
    """Get LLM manager from app state."""
    if not hasattr(app.state, 'llm_manager'):
        raise HTTPException(status_code=503, detail="LLM Manager not initialized")
    return app.state.llm_manager


def get_context_engine() -> ContextEngine:
    """Get context engine from app state."""
    if not hasattr(app.state, 'context_engine'):
        raise HTTPException(status_code=503, detail="Context Engine not initialized")
    return app.state.context_engine


@router.post("/generate", response_model=CodeSnippet)
async def generate_code(
    request: CodeGenerationRequest,
    llm_manager: LLMManager = Depends(get_llm_manager)
):
    """Generate code from a prompt."""
    try:
        logger.info(
            "Code generation request",
            language=request.language,
            prompt_length=len(request.prompt)
        )
        
        generated_code = await llm_manager.generate_code(
            prompt=request.prompt,
            language=request.language,
            context=request.context
        )
        
        return CodeSnippet(
            content=generated_code,
            language=request.language,
            metadata={
                "generated": True,
                "prompt": request.prompt[:100] + "..." if len(request.prompt) > 100 else request.prompt
            }
        )
        
    except Exception as e:
        logger.error("Code generation failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Code generation failed: {str(e)}")


@router.post("/analyze", response_model=CodeAnalysisResult)
async def analyze_code(
    code_snippet: CodeSnippet,
    analysis_type: str = "general",
    llm_manager: LLMManager = Depends(get_llm_manager)
):
    """Analyze code and provide insights."""
    try:
        logger.info(
            "Code analysis request",
            language=code_snippet.language,
            analysis_type=analysis_type,
            code_length=len(code_snippet.content)
        )
        
        analysis = await llm_manager.analyze_code(
            code=code_snippet.content,
            language=code_snippet.language,
            analysis_type=analysis_type
        )
        
        # Parse analysis into structured format
        # This is a simplified version - in practice, you'd use more sophisticated parsing
        lines = analysis.split('\n')
        suggestions = [line.strip('- ') for line in lines if line.strip().startswith('-')]
        
        return CodeAnalysisResult(
            analysis=analysis,
            suggestions=suggestions[:5],  # Top 5 suggestions
            issues=[],  # Would be populated by more detailed analysis
            metrics={"lines": len(code_snippet.content.split('\n'))}
        )
        
    except Exception as e:
        logger.error("Code analysis failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Code analysis failed: {str(e)}")


@router.post("/improve", response_model=CodeSnippet)
async def improve_code(
    request: CodeImprovementRequest,
    llm_manager: LLMManager = Depends(get_llm_manager)
):
    """Improve existing code based on the specified improvement type."""
    try:
        logger.info(
            "Code improvement request",
            language=request.language,
            improvement_type=request.improvement_type
        )
        
        improvement_prompts = {
            "general": "Improve this code for better readability, maintainability, and best practices",
            "performance": "Optimize this code for better performance and efficiency",
            "security": "Improve this code to address security vulnerabilities and risks",
            "style": "Refactor this code to follow proper style guidelines and conventions"
        }
        
        prompt = improvement_prompts.get(
            request.improvement_type,
            improvement_prompts["general"]
        )
        
        improved_code = await llm_manager.generate_code(
            prompt=f"{prompt}:\n\n```{request.language}\n{request.code}\n```",
            language=request.language
        )
        
        return CodeSnippet(
            content=improved_code,
            language=request.language,
            metadata={
                "improved": True,
                "improvement_type": request.improvement_type,
                "original_length": len(request.code)
            }
        )
        
    except Exception as e:
        logger.error("Code improvement failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Code improvement failed: {str(e)}")


@router.post("/upload", response_model=Dict[str, str])
async def upload_code_file(
    file: UploadFile = File(...),
    language: Optional[str] = None,
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Upload a code file and add it to the context."""
    try:
        # Read file content
        content = await file.read()
        code_content = content.decode('utf-8')
        
        # Detect language if not provided
        if not language:
            language = _detect_language_from_filename(file.filename)
        
        # Extract symbols and dependencies (simplified)
        symbols = _extract_symbols(code_content, language)
        dependencies = _extract_dependencies(code_content, language)
        
        # Create code context
        code_context = CodeContext(
            file_path=file.filename,
            language=language,
            content=code_content,
            symbols=symbols,
            dependencies=dependencies,
            metadata={
                "uploaded": True,
                "file_size": len(code_content)
            }
        )
        
        # Add to context engine
        context_id = await context_engine.add_code_context(code_context)
        
        logger.info(
            "Code file uploaded",
            filename=file.filename,
            language=language,
            context_id=context_id
        )
        
        return {
            "message": "File uploaded successfully",
            "context_id": context_id,
            "language": language,
            "symbols_found": len(symbols)
        }
        
    except Exception as e:
        logger.error("File upload failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"File upload failed: {str(e)}")


@router.get("/context/search")
async def search_code_context(
    query: str,
    limit: int = 10,
    context_engine: ContextEngine = Depends(get_context_engine)
):
    """Search code context using the query."""
    try:
        results = await context_engine.search_memory(query, limit=limit)
        
        return {
            "query": query,
            "results": [
                {
                    "id": item.id,
                    "content_preview": item.content[:200] + "..." if len(item.content) > 200 else item.content,
                    "metadata": item.metadata,
                    "relevance": item.importance
                }
                for item in results
            ]
        }
        
    except Exception as e:
        logger.error("Context search failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Context search failed: {str(e)}")


def _detect_language_from_filename(filename: str) -> str:
    """Detect programming language from filename extension."""
    extension_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.java': 'java',
        '.cpp': 'cpp',
        '.c': 'c',
        '.go': 'go',
        '.rs': 'rust',
        '.php': 'php',
        '.rb': 'ruby',
        '.swift': 'swift',
        '.kt': 'kotlin',
        '.scala': 'scala',
        '.cs': 'csharp',
        '.html': 'html',
        '.css': 'css',
        '.sql': 'sql',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.json': 'json',
        '.md': 'markdown'
    }
    
    for ext, lang in extension_map.items():
        if filename.lower().endswith(ext):
            return lang
    
    return 'text'


def _extract_symbols(code: str, language: str) -> List[str]:
    """Extract symbols (functions, classes, etc.) from code."""
    symbols = []
    
    if language == 'python':
        import re
        # Simple regex patterns for Python
        class_pattern = r'class\s+(\w+)'
        function_pattern = r'def\s+(\w+)'
        
        symbols.extend(re.findall(class_pattern, code))
        symbols.extend(re.findall(function_pattern, code))
    
    elif language in ['javascript', 'typescript']:
        import re
        # Simple regex patterns for JS/TS
        function_pattern = r'function\s+(\w+)|(\w+)\s*=\s*function|(\w+)\s*=>\s*'
        class_pattern = r'class\s+(\w+)'
        
        symbols.extend(re.findall(class_pattern, code))
        # Flatten the tuple results from function pattern
        for match in re.findall(function_pattern, code):
            symbols.extend([m for m in match if m])
    
    return list(set(symbols))  # Remove duplicates


def _extract_dependencies(code: str, language: str) -> List[str]:
    """Extract dependencies (imports, includes, etc.) from code."""
    dependencies = []
    
    if language == 'python':
        import re
        import_pattern = r'(?:from\s+(\S+)\s+import|import\s+(\S+))'
        matches = re.findall(import_pattern, code)
        for match in matches:
            dependencies.extend([m for m in match if m])
    
    elif language in ['javascript', 'typescript']:
        import re
        import_pattern = r'import.*from\s+[\'"]([^\'"]+)[\'"]|require\([\'"]([^\'"]+)[\'"]\)'
        matches = re.findall(import_pattern, code)
        for match in matches:
            dependencies.extend([m for m in match if m])
    
    return list(set(dependencies))  # Remove duplicates
