"""
Agent-related API endpoints for the Agentic Code Generation System.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from src.agents.orchestrator import AgentOrchestrator, WorkflowRequest, WorkflowResult
from src.core.logging import get_logger
from src.main import app

router = APIRouter()
logger = get_logger(__name__)


class CodeGenerationRequest(BaseModel):
    """Request for code generation."""
    prompt: str = Field(..., description="Description of the code to generate")
    language: str = Field(default="python", description="Programming language")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")
    workflow_type: str = Field(default="code_generation", description="Type of workflow to execute")
    include_tests: bool = Field(default=False, description="Whether to generate tests")
    include_analysis: bool = Field(default=True, description="Whether to include code analysis")


class CodeAnalysisRequest(BaseModel):
    """Request for code analysis."""
    code: str = Field(..., description="Code to analyze")
    language: str = Field(default="python", description="Programming language")
    analysis_type: str = Field(default="general", description="Type of analysis")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional context")


class WorkflowStatusResponse(BaseModel):
    """Response for workflow status."""
    task_id: str
    status: str
    progress: Dict[str, Any]
    result: Optional[WorkflowResult] = None


def get_orchestrator() -> AgentOrchestrator:
    """Get the agent orchestrator from app state."""
    if not hasattr(app.state, 'llm_manager') or not hasattr(app.state, 'context_engine'):
        raise HTTPException(status_code=503, detail="System not initialized")
    
    # Create orchestrator on demand (could be cached in app state)
    return AgentOrchestrator(app.state.llm_manager, app.state.context_engine)


@router.post("/generate-code", response_model=WorkflowResult)
async def generate_code(
    request: CodeGenerationRequest,
    background_tasks: BackgroundTasks,
    orchestrator: AgentOrchestrator = Depends(get_orchestrator)
):
    """Generate code using the agent system."""
    try:
        logger.info(
            "Code generation request received",
            language=request.language,
            workflow_type=request.workflow_type
        )
        
        # Determine workflow type based on request
        workflow_type = request.workflow_type
        if request.include_tests and workflow_type == "code_generation":
            workflow_type = "full_development"
        
        # Create workflow request
        workflow_request = WorkflowRequest(
            task_type="code_generation",
            prompt=request.prompt,
            context={
                "language": request.language,
                "include_tests": request.include_tests,
                "include_analysis": request.include_analysis,
                **request.context
            },
            language=request.language,
            workflow_type=workflow_type
        )
        
        # Execute workflow
        result = await orchestrator.execute_workflow(workflow_request)
        
        logger.info(
            "Code generation completed",
            task_id=result.task_id,
            success=result.success
        )
        
        return result
        
    except Exception as e:
        logger.error("Code generation failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Code generation failed: {str(e)}")


@router.post("/analyze-code", response_model=WorkflowResult)
async def analyze_code(
    request: CodeAnalysisRequest,
    orchestrator: AgentOrchestrator = Depends(get_orchestrator)
):
    """Analyze code using the agent system."""
    try:
        logger.info(
            "Code analysis request received",
            language=request.language,
            analysis_type=request.analysis_type
        )
        
        # Create workflow request
        workflow_request = WorkflowRequest(
            task_type="code_analysis",
            prompt=f"Analyze this {request.language} code",
            context={
                "code": request.code,
                "language": request.language,
                "analysis_type": request.analysis_type,
                **request.context
            },
            language=request.language,
            workflow_type="code_analysis"
        )
        
        # Execute workflow
        result = await orchestrator.execute_workflow(workflow_request)
        
        logger.info(
            "Code analysis completed",
            task_id=result.task_id,
            success=result.success
        )
        
        return result
        
    except Exception as e:
        logger.error("Code analysis failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Code analysis failed: {str(e)}")


@router.get("/workflows", response_model=List[str])
async def get_available_workflows(
    orchestrator: AgentOrchestrator = Depends(get_orchestrator)
):
    """Get list of available workflows."""
    return orchestrator.get_available_workflows()


@router.get("/workflows/{workflow_type}", response_model=Dict[str, Any])
async def get_workflow_info(
    workflow_type: str,
    orchestrator: AgentOrchestrator = Depends(get_orchestrator)
):
    """Get information about a specific workflow."""
    info = orchestrator.get_workflow_info(workflow_type)
    if not info:
        raise HTTPException(status_code=404, detail=f"Workflow '{workflow_type}' not found")
    return info


@router.get("/capabilities", response_model=Dict[str, List[str]])
async def get_agent_capabilities(
    orchestrator: AgentOrchestrator = Depends(get_orchestrator)
):
    """Get capabilities of all available agents."""
    capabilities = {}
    for agent_name, agent in orchestrator.agents.items():
        capabilities[agent_name] = agent.capabilities
    return capabilities


@router.post("/custom-workflow", response_model=WorkflowResult)
async def execute_custom_workflow(
    request: WorkflowRequest,
    orchestrator: AgentOrchestrator = Depends(get_orchestrator)
):
    """Execute a custom workflow with full control over parameters."""
    try:
        logger.info(
            "Custom workflow request received",
            task_type=request.task_type,
            workflow_type=request.workflow_type
        )
        
        # Execute workflow
        result = await orchestrator.execute_workflow(request)
        
        logger.info(
            "Custom workflow completed",
            task_id=result.task_id,
            success=result.success
        )
        
        return result
        
    except Exception as e:
        logger.error("Custom workflow failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Workflow execution failed: {str(e)}")


# WebSocket endpoint for real-time workflow updates (placeholder)
# This would be implemented with FastAPI WebSocket support
@router.get("/status/{task_id}", response_model=WorkflowStatusResponse)
async def get_workflow_status(task_id: str):
    """Get status of a running workflow."""
    # This is a placeholder - in a real implementation, you'd track
    # workflow status in a database or cache
    return WorkflowStatusResponse(
        task_id=task_id,
        status="completed",  # or "running", "failed", etc.
        progress={"step": "completed", "percentage": 100}
    )
