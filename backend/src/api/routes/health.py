"""
Health check endpoints for the Agentic Code Generation System.
"""

from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from src.core.config import get_settings
from src.core.logging import get_logger

router = APIRouter()
logger = get_logger(__name__)
settings = get_settings()


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime
    version: str
    components: Dict[str, Any]


class ComponentStatus(BaseModel):
    """Individual component status."""
    status: str
    details: Dict[str, Any] = {}
    error: str = None


@router.get("/", response_model=HealthResponse)
async def health_check():
    """Basic health check endpoint."""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        version="1.0.0",
        components={}
    )


@router.get("/detailed", response_model=HealthResponse)
async def detailed_health_check():
    """Detailed health check with component status."""
    components = {}
    overall_status = "healthy"
    
    # Check LLM Manager
    try:
        from src.main import app
        if hasattr(app.state, 'llm_manager'):
            llm_manager = app.state.llm_manager
            providers = llm_manager.get_available_providers()
            components["llm_manager"] = ComponentStatus(
                status="healthy",
                details={
                    "providers": providers,
                    "default_provider": settings.default_llm_provider
                }
            ).dict()
        else:
            components["llm_manager"] = ComponentStatus(
                status="not_initialized",
                error="LLM Manager not initialized"
            ).dict()
            overall_status = "degraded"
    except Exception as e:
        components["llm_manager"] = ComponentStatus(
            status="error",
            error=str(e)
        ).dict()
        overall_status = "unhealthy"
    
    # Check Context Engine
    try:
        from src.main import app
        if hasattr(app.state, 'context_engine'):
            context_engine = app.state.context_engine
            summary = await context_engine.get_context_summary()
            components["context_engine"] = ComponentStatus(
                status="healthy",
                details=summary
            ).dict()
        else:
            components["context_engine"] = ComponentStatus(
                status="not_initialized",
                error="Context Engine not initialized"
            ).dict()
            overall_status = "degraded"
    except Exception as e:
        components["context_engine"] = ComponentStatus(
            status="error",
            error=str(e)
        ).dict()
        overall_status = "unhealthy"
    
    # Check Vector Database (ChromaDB)
    try:
        import chromadb
        client = chromadb.HttpClient(
            host=settings.chroma_host,
            port=settings.chroma_port
        )
        # Try to list collections
        collections = client.list_collections()
        components["vector_database"] = ComponentStatus(
            status="healthy",
            details={
                "type": "chromadb",
                "host": settings.chroma_host,
                "port": settings.chroma_port,
                "collections": len(collections)
            }
        ).dict()
    except Exception as e:
        components["vector_database"] = ComponentStatus(
            status="error",
            error=str(e),
            details={"type": "chromadb"}
        ).dict()
        if overall_status == "healthy":
            overall_status = "degraded"
    
    # Check Graph Database (Neo4j)
    try:
        from neo4j import GraphDatabase
        driver = GraphDatabase.driver(
            settings.neo4j_uri,
            auth=(settings.neo4j_user, settings.neo4j_password)
        )
        with driver.session() as session:
            result = session.run("RETURN 1")
            result.single()
        driver.close()
        
        components["graph_database"] = ComponentStatus(
            status="healthy",
            details={
                "type": "neo4j",
                "uri": settings.neo4j_uri
            }
        ).dict()
    except Exception as e:
        components["graph_database"] = ComponentStatus(
            status="error",
            error=str(e),
            details={"type": "neo4j"}
        ).dict()
        if overall_status == "healthy":
            overall_status = "degraded"
    
    return HealthResponse(
        status=overall_status,
        timestamp=datetime.now(),
        version="1.0.0",
        components=components
    )


@router.get("/ready")
async def readiness_check():
    """Readiness check for Kubernetes/container orchestration."""
    try:
        from src.main import app
        
        # Check if core components are initialized
        if not hasattr(app.state, 'llm_manager'):
            raise HTTPException(status_code=503, detail="LLM Manager not ready")
        
        if not hasattr(app.state, 'context_engine'):
            raise HTTPException(status_code=503, detail="Context Engine not ready")
        
        return {"status": "ready", "timestamp": datetime.now()}
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")


@router.get("/live")
async def liveness_check():
    """Liveness check for Kubernetes/container orchestration."""
    return {"status": "alive", "timestamp": datetime.now()}
