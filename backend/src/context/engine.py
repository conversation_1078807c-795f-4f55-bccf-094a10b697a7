"""
Context Engine with brain-like memory patterns for the Agentic Code Generation System.
"""

import asyncio
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional, Tuple

import chromadb
import numpy as np
from neo4j import AsyncGraphDatabase
from pydantic import BaseModel
from sentence_transformers import SentenceTransformer

from src.core.config import get_settings
from src.core.logging import LoggerMixin


class MemoryItem(BaseModel):
    """Represents an item in memory."""
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    timestamp: datetime
    access_count: int = 0
    importance: float = 0.5
    memory_type: str = "working"  # working, short_term, long_term


class CodeContext(BaseModel):
    """Represents code context information."""
    file_path: str
    language: str
    content: str
    symbols: List[str]
    dependencies: List[str]
    metadata: Dict[str, Any]


class ContextEngine(LoggerMixin):
    """
    Brain-like context engine that manages different types of memory:
    - Working Memory: Current task context (limited capacity)
    - Short-term Memory: Recent interactions and temporary context
    - Long-term Memory: Consolidated knowledge and learned patterns
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.embedding_model = None
        self.chroma_client = None
        self.chroma_collection = None
        self.neo4j_driver = None
        
        # Memory stores
        self.working_memory: List[MemoryItem] = []
        self.short_term_memory: List[MemoryItem] = []
        
        # Memory limits
        self.working_memory_limit = self.settings.working_memory_size
        self.short_term_memory_limit = self.settings.short_term_memory_size
        
    async def initialize(self) -> None:
        """Initialize the context engine and all memory systems."""
        self.logger.info("Initializing Context Engine")
        
        # Initialize embedding model
        self.logger.info("Loading embedding model")
        self.embedding_model = SentenceTransformer(self.settings.embedding_model)
        
        # Initialize ChromaDB (Vector Database)
        await self._initialize_chroma()
        
        # Initialize Neo4j (Graph Database)
        await self._initialize_neo4j()
        
        self.logger.info("Context Engine initialized successfully")
    
    async def _initialize_chroma(self) -> None:
        """Initialize ChromaDB for vector storage."""
        try:
            self.chroma_client = chromadb.HttpClient(
                host=self.settings.chroma_host,
                port=self.settings.chroma_port
            )
            
            # Get or create collection
            try:
                self.chroma_collection = self.chroma_client.get_collection(
                    name=self.settings.chroma_collection_name
                )
            except Exception:
                self.chroma_collection = self.chroma_client.create_collection(
                    name=self.settings.chroma_collection_name,
                    metadata={"description": "Agentic code context storage"}
                )
            
            self.logger.info("ChromaDB initialized successfully")
            
        except Exception as e:
            self.logger.warning(
                "Failed to connect to ChromaDB, using in-memory storage",
                error=str(e)
            )
            # Fallback to in-memory ChromaDB
            self.chroma_client = chromadb.Client()
            self.chroma_collection = self.chroma_client.create_collection(
                name=self.settings.chroma_collection_name
            )
    
    async def _initialize_neo4j(self) -> None:
        """Initialize Neo4j for graph storage."""
        try:
            self.neo4j_driver = AsyncGraphDatabase.driver(
                self.settings.neo4j_uri,
                auth=(self.settings.neo4j_user, self.settings.neo4j_password)
            )
            
            # Test connection
            async with self.neo4j_driver.session() as session:
                await session.run("RETURN 1")
            
            self.logger.info("Neo4j initialized successfully")
            
        except Exception as e:
            self.logger.warning(
                "Failed to connect to Neo4j, graph features disabled",
                error=str(e)
            )
            self.neo4j_driver = None
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text."""
        if self.embedding_model is None:
            return []
        return self.embedding_model.encode(text).tolist()
    
    async def add_to_working_memory(
        self,
        content: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Add item to working memory with capacity management."""
        item_id = f"wm_{datetime.now().isoformat()}_{len(self.working_memory)}"
        
        memory_item = MemoryItem(
            id=item_id,
            content=content,
            metadata=metadata or {},
            embedding=self._generate_embedding(content),
            timestamp=datetime.now(),
            memory_type="working"
        )
        
        self.working_memory.append(memory_item)
        
        # Manage capacity - move oldest items to short-term memory
        if len(self.working_memory) > self.working_memory_limit:
            oldest_item = self.working_memory.pop(0)
            await self._move_to_short_term_memory(oldest_item)
        
        self.logger.debug(
            "Added item to working memory",
            item_id=item_id,
            working_memory_size=len(self.working_memory)
        )
        
        return item_id
    
    async def _move_to_short_term_memory(self, item: MemoryItem) -> None:
        """Move item from working memory to short-term memory."""
        item.memory_type = "short_term"
        self.short_term_memory.append(item)
        
        # Manage short-term memory capacity
        if len(self.short_term_memory) > self.short_term_memory_limit:
            # Consolidate oldest items to long-term memory
            oldest_items = self.short_term_memory[:10]  # Batch consolidation
            self.short_term_memory = self.short_term_memory[10:]
            
            await self._consolidate_to_long_term_memory(oldest_items)
    
    async def _consolidate_to_long_term_memory(self, items: List[MemoryItem]) -> None:
        """Consolidate items to long-term memory (vector database)."""
        if not self.chroma_collection:
            return
        
        try:
            # Prepare data for ChromaDB
            ids = [item.id for item in items]
            documents = [item.content for item in items]
            embeddings = [item.embedding for item in items if item.embedding]
            metadatas = [
                {
                    **item.metadata,
                    "timestamp": item.timestamp.isoformat(),
                    "access_count": item.access_count,
                    "importance": item.importance,
                    "memory_type": "long_term"
                }
                for item in items
            ]
            
            # Add to ChromaDB
            if embeddings:
                self.chroma_collection.add(
                    ids=ids,
                    documents=documents,
                    embeddings=embeddings,
                    metadatas=metadatas
                )
            else:
                self.chroma_collection.add(
                    ids=ids,
                    documents=documents,
                    metadatas=metadatas
                )
            
            self.logger.info(
                "Consolidated items to long-term memory",
                count=len(items)
            )
            
        except Exception as e:
            self.logger.error(
                "Failed to consolidate to long-term memory",
                error=str(e)
            )
    
    async def search_memory(
        self,
        query: str,
        memory_types: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[MemoryItem]:
        """Search across all memory types."""
        results = []
        memory_types = memory_types or ["working", "short_term", "long_term"]
        
        # Search working memory
        if "working" in memory_types:
            results.extend(await self._search_working_memory(query, limit))
        
        # Search short-term memory
        if "short_term" in memory_types:
            results.extend(await self._search_short_term_memory(query, limit))
        
        # Search long-term memory
        if "long_term" in memory_types:
            results.extend(await self._search_long_term_memory(query, limit))
        
        # Sort by relevance and recency
        results.sort(key=lambda x: (x.importance, x.timestamp), reverse=True)
        
        return results[:limit]
    
    async def _search_working_memory(self, query: str, limit: int) -> List[MemoryItem]:
        """Search working memory."""
        query_embedding = self._generate_embedding(query)
        if not query_embedding:
            return []
        
        results = []
        for item in self.working_memory:
            if item.embedding:
                similarity = self._cosine_similarity(query_embedding, item.embedding)
                if similarity > 0.5:  # Threshold
                    item.importance = similarity
                    results.append(item)
        
        return sorted(results, key=lambda x: x.importance, reverse=True)[:limit]
    
    async def _search_short_term_memory(self, query: str, limit: int) -> List[MemoryItem]:
        """Search short-term memory."""
        query_embedding = self._generate_embedding(query)
        if not query_embedding:
            return []
        
        results = []
        for item in self.short_term_memory:
            if item.embedding:
                similarity = self._cosine_similarity(query_embedding, item.embedding)
                if similarity > 0.5:  # Threshold
                    item.importance = similarity
                    results.append(item)
        
        return sorted(results, key=lambda x: x.importance, reverse=True)[:limit]
    
    async def _search_long_term_memory(self, query: str, limit: int) -> List[MemoryItem]:
        """Search long-term memory using ChromaDB."""
        if not self.chroma_collection:
            return []
        
        try:
            results = self.chroma_collection.query(
                query_texts=[query],
                n_results=limit
            )
            
            memory_items = []
            for i, doc_id in enumerate(results["ids"][0]):
                memory_items.append(MemoryItem(
                    id=doc_id,
                    content=results["documents"][0][i],
                    metadata=results["metadatas"][0][i],
                    timestamp=datetime.fromisoformat(
                        results["metadatas"][0][i]["timestamp"]
                    ),
                    access_count=results["metadatas"][0][i].get("access_count", 0),
                    importance=results["distances"][0][i] if "distances" in results else 0.5,
                    memory_type="long_term"
                ))
            
            return memory_items
            
        except Exception as e:
            self.logger.error("Failed to search long-term memory", error=str(e))
            return []
    
    def _cosine_similarity(self, a: List[float], b: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        if not a or not b:
            return 0.0
        
        a_np = np.array(a)
        b_np = np.array(b)
        
        return np.dot(a_np, b_np) / (np.linalg.norm(a_np) * np.linalg.norm(b_np))
    
    async def add_code_context(self, context: CodeContext) -> str:
        """Add code context to the memory system."""
        content = f"""
        File: {context.file_path}
        Language: {context.language}
        Symbols: {', '.join(context.symbols)}
        Dependencies: {', '.join(context.dependencies)}
        
        Content:
        {context.content}
        """
        
        metadata = {
            "type": "code_context",
            "file_path": context.file_path,
            "language": context.language,
            "symbols": context.symbols,
            "dependencies": context.dependencies,
            **context.metadata
        }
        
        # Add to working memory
        item_id = await self.add_to_working_memory(content, metadata)
        
        # Add relationships to graph database
        if self.neo4j_driver:
            await self._add_code_relationships(context)
        
        return item_id
    
    async def _add_code_relationships(self, context: CodeContext) -> None:
        """Add code relationships to Neo4j graph database."""
        if not self.neo4j_driver:
            return
        
        try:
            async with self.neo4j_driver.session() as session:
                # Create file node
                await session.run(
                    """
                    MERGE (f:File {path: $path})
                    SET f.language = $language,
                        f.updated = datetime()
                    """,
                    path=context.file_path,
                    language=context.language
                )
                
                # Create symbol nodes and relationships
                for symbol in context.symbols:
                    await session.run(
                        """
                        MERGE (s:Symbol {name: $symbol})
                        MERGE (f:File {path: $path})
                        MERGE (f)-[:CONTAINS]->(s)
                        """,
                        symbol=symbol,
                        path=context.file_path
                    )
                
                # Create dependency relationships
                for dep in context.dependencies:
                    await session.run(
                        """
                        MERGE (d:File {path: $dep})
                        MERGE (f:File {path: $path})
                        MERGE (f)-[:DEPENDS_ON]->(d)
                        """,
                        dep=dep,
                        path=context.file_path
                    )
                    
        except Exception as e:
            self.logger.error("Failed to add code relationships", error=str(e))
    
    async def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of current context state."""
        return {
            "working_memory": {
                "count": len(self.working_memory),
                "limit": self.working_memory_limit,
                "items": [
                    {
                        "id": item.id,
                        "content_preview": item.content[:100] + "..." if len(item.content) > 100 else item.content,
                        "timestamp": item.timestamp.isoformat(),
                        "metadata": item.metadata
                    }
                    for item in self.working_memory[-5:]  # Last 5 items
                ]
            },
            "short_term_memory": {
                "count": len(self.short_term_memory),
                "limit": self.short_term_memory_limit
            },
            "long_term_memory": {
                "available": self.chroma_collection is not None,
                "collection_name": self.settings.chroma_collection_name if self.chroma_collection else None
            },
            "graph_database": {
                "available": self.neo4j_driver is not None
            }
        }
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        self.logger.info("Cleaning up Context Engine")
        
        if self.neo4j_driver:
            await self.neo4j_driver.close()
        
        # Consolidate remaining short-term memory
        if self.short_term_memory:
            await self._consolidate_to_long_term_memory(self.short_term_memory)
        
        self.logger.info("Context Engine cleanup complete")
