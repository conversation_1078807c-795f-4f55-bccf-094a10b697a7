"""
Agent orchestrator using LangGraph for coordinating multiple AI agents.
"""

import asyncio
import uuid
from typing import Any, Dict, List, Optional

from langgraph.graph import StateGraph, END
from pydantic import BaseModel

from src.agents.base import (
    AgentState, 
    AgentResult, 
    BaseAgent,
    CodeGeneratorAgent,
    CodeAnalyzerAgent,
    TestGeneratorAgent
)
from src.core.config import get_settings
from src.core.logging import LoggerMixin, log_agent_action
from src.llm.manager import LLMManager
from src.context.engine import ContextEngine


class WorkflowRequest(BaseModel):
    """Request for agent workflow execution."""
    task_type: str
    prompt: str
    context: Dict[str, Any] = {}
    language: str = "python"
    workflow_type: str = "code_generation"
    metadata: Dict[str, Any] = {}


class WorkflowResult(BaseModel):
    """Result from workflow execution."""
    task_id: str
    success: bool
    outputs: Dict[str, Any]
    artifacts: Dict[str, Any]
    execution_log: List[Dict[str, Any]]
    error: Optional[str] = None
    metadata: Dict[str, Any] = {}


class AgentOrchestrator(LoggerMixin):
    """Orchestrates multiple agents using LangGraph workflows."""
    
    def __init__(self, llm_manager: LLMManager, context_engine: ContextEngine):
        self.llm_manager = llm_manager
        self.context_engine = context_engine
        self.settings = get_settings()
        
        # Initialize agents
        self.agents = {
            "code_generator": CodeGeneratorAgent(
                "CodeGenerator", llm_manager, context_engine
            ),
            "code_analyzer": CodeAnalyzerAgent(
                "CodeAnalyzer", llm_manager, context_engine
            ),
            "test_generator": TestGeneratorAgent(
                "TestGenerator", llm_manager, context_engine
            ),
        }
        
        # Define workflows
        self.workflows = {
            "code_generation": self._create_code_generation_workflow(),
            "code_analysis": self._create_code_analysis_workflow(),
            "full_development": self._create_full_development_workflow(),
        }
    
    def _create_code_generation_workflow(self) -> StateGraph:
        """Create a workflow for code generation."""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("generate_code", self._generate_code_node)
        workflow.add_node("analyze_code", self._analyze_code_node)
        workflow.add_node("finalize", self._finalize_node)
        
        # Add edges
        workflow.set_entry_point("generate_code")
        workflow.add_edge("generate_code", "analyze_code")
        workflow.add_edge("analyze_code", "finalize")
        workflow.add_edge("finalize", END)
        
        return workflow.compile()
    
    def _create_code_analysis_workflow(self) -> StateGraph:
        """Create a workflow for code analysis."""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("analyze_code", self._analyze_code_node)
        workflow.add_node("suggest_improvements", self._suggest_improvements_node)
        workflow.add_node("finalize", self._finalize_node)
        
        # Add edges
        workflow.set_entry_point("analyze_code")
        workflow.add_edge("analyze_code", "suggest_improvements")
        workflow.add_edge("suggest_improvements", "finalize")
        workflow.add_edge("finalize", END)
        
        return workflow.compile()
    
    def _create_full_development_workflow(self) -> StateGraph:
        """Create a comprehensive development workflow."""
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("generate_code", self._generate_code_node)
        workflow.add_node("analyze_code", self._analyze_code_node)
        workflow.add_node("generate_tests", self._generate_tests_node)
        workflow.add_node("review_and_improve", self._review_and_improve_node)
        workflow.add_node("finalize", self._finalize_node)
        
        # Add edges
        workflow.set_entry_point("generate_code")
        workflow.add_edge("generate_code", "analyze_code")
        workflow.add_edge("analyze_code", "generate_tests")
        workflow.add_edge("generate_tests", "review_and_improve")
        workflow.add_edge("review_and_improve", "finalize")
        workflow.add_edge("finalize", END)
        
        return workflow.compile()
    
    async def _generate_code_node(self, state: AgentState) -> AgentState:
        """Node for code generation."""
        self.logger.info(
            "Executing code generation node",
            **log_agent_action("orchestrator", "generate_code", task_id=state.task_id)
        )
        
        result = await self.agents["code_generator"].execute(state)
        
        if result.success:
            state.artifacts.update(result.artifacts)
            state.completed_steps.append("generate_code")
            state.current_step = result.next_step or "analyze_code"
        else:
            state.error = result.error
            state.current_step = "error"
        
        return state
    
    async def _analyze_code_node(self, state: AgentState) -> AgentState:
        """Node for code analysis."""
        self.logger.info(
            "Executing code analysis node",
            **log_agent_action("orchestrator", "analyze_code", task_id=state.task_id)
        )
        
        result = await self.agents["code_analyzer"].execute(state)
        
        if result.success:
            state.artifacts.update(result.artifacts)
            state.completed_steps.append("analyze_code")
            state.current_step = result.next_step or "generate_tests"
        else:
            state.error = result.error
            state.current_step = "error"
        
        return state
    
    async def _generate_tests_node(self, state: AgentState) -> AgentState:
        """Node for test generation."""
        self.logger.info(
            "Executing test generation node",
            **log_agent_action("orchestrator", "generate_tests", task_id=state.task_id)
        )
        
        result = await self.agents["test_generator"].execute(state)
        
        if result.success:
            state.artifacts.update(result.artifacts)
            state.completed_steps.append("generate_tests")
            state.current_step = result.next_step or "review_and_improve"
        else:
            state.error = result.error
            state.current_step = "error"
        
        return state
    
    async def _suggest_improvements_node(self, state: AgentState) -> AgentState:
        """Node for suggesting improvements."""
        self.logger.info(
            "Executing improvement suggestions node",
            **log_agent_action("orchestrator", "suggest_improvements", task_id=state.task_id)
        )
        
        # Use code analyzer to suggest improvements
        state.context["analysis_type"] = "improvement"
        result = await self.agents["code_analyzer"].execute(state)
        
        if result.success:
            state.artifacts["improvements"] = result.output
            state.completed_steps.append("suggest_improvements")
            state.current_step = "finalize"
        else:
            state.error = result.error
            state.current_step = "error"
        
        return state
    
    async def _review_and_improve_node(self, state: AgentState) -> AgentState:
        """Node for reviewing and improving the generated code."""
        self.logger.info(
            "Executing review and improve node",
            **log_agent_action("orchestrator", "review_and_improve", task_id=state.task_id)
        )
        
        # Analyze the generated code and tests together
        code = state.artifacts.get("generated_code", "")
        tests = state.artifacts.get("test_code", "")
        analysis = state.artifacts.get("analysis", "")
        
        # Create improvement suggestions
        improvement_prompt = f"""
        Based on the following code, tests, and analysis, provide specific improvement suggestions:
        
        Code:
        {code}
        
        Tests:
        {tests}
        
        Analysis:
        {analysis}
        
        Focus on:
        1. Code quality and best practices
        2. Test coverage and quality
        3. Performance optimizations
        4. Security considerations
        5. Documentation improvements
        """
        
        state.messages.append({
            "role": "user",
            "content": improvement_prompt
        })
        
        # Get improvements from code analyzer
        state.context["analysis_type"] = "comprehensive"
        result = await self.agents["code_analyzer"].execute(state)
        
        if result.success:
            state.artifacts["comprehensive_review"] = result.output
            state.completed_steps.append("review_and_improve")
            state.current_step = "finalize"
        else:
            state.error = result.error
            state.current_step = "error"
        
        return state
    
    async def _finalize_node(self, state: AgentState) -> AgentState:
        """Node for finalizing the workflow."""
        self.logger.info(
            "Executing finalize node",
            **log_agent_action("orchestrator", "finalize", task_id=state.task_id)
        )
        
        state.completed_steps.append("finalize")
        state.current_step = "completed"
        
        # Add final summary to context
        summary = f"""
        Workflow completed successfully.
        Steps completed: {', '.join(state.completed_steps)}
        Artifacts generated: {', '.join(state.artifacts.keys())}
        """
        
        await self.context_engine.add_to_working_memory(
            summary,
            {
                "type": "workflow_completion",
                "task_id": state.task_id,
                "artifacts": list(state.artifacts.keys())
            }
        )
        
        return state
    
    async def execute_workflow(self, request: WorkflowRequest) -> WorkflowResult:
        """Execute a workflow based on the request."""
        task_id = str(uuid.uuid4())
        
        self.logger.info(
            "Starting workflow execution",
            task_id=task_id,
            workflow_type=request.workflow_type,
            task_type=request.task_type
        )
        
        # Create initial state
        initial_state = AgentState(
            task_id=task_id,
            messages=[{"role": "user", "content": request.prompt}],
            context={
                "language": request.language,
                "task_type": request.task_type,
                **request.context
            },
            metadata=request.metadata
        )
        
        try:
            # Get the appropriate workflow
            workflow = self.workflows.get(request.workflow_type)
            if not workflow:
                raise ValueError(f"Unknown workflow type: {request.workflow_type}")
            
            # Execute the workflow
            final_state = await workflow.ainvoke(initial_state)
            
            # Create execution log
            execution_log = [
                {
                    "step": step,
                    "timestamp": final_state.metadata.get(f"{step}_timestamp"),
                    "status": "completed"
                }
                for step in final_state.completed_steps
            ]
            
            # Prepare outputs
            outputs = {}
            if "generated_code" in final_state.artifacts:
                outputs["code"] = final_state.artifacts["generated_code"]
            if "analysis" in final_state.artifacts:
                outputs["analysis"] = final_state.artifacts["analysis"]
            if "test_code" in final_state.artifacts:
                outputs["tests"] = final_state.artifacts["test_code"]
            if "comprehensive_review" in final_state.artifacts:
                outputs["review"] = final_state.artifacts["comprehensive_review"]
            
            return WorkflowResult(
                task_id=task_id,
                success=final_state.error is None,
                outputs=outputs,
                artifacts=final_state.artifacts,
                execution_log=execution_log,
                error=final_state.error,
                metadata=final_state.metadata
            )
            
        except Exception as e:
            self.logger.error(
                "Workflow execution failed",
                task_id=task_id,
                error=str(e)
            )
            
            return WorkflowResult(
                task_id=task_id,
                success=False,
                outputs={},
                artifacts={},
                execution_log=[],
                error=str(e)
            )
    
    def get_available_workflows(self) -> List[str]:
        """Get list of available workflows."""
        return list(self.workflows.keys())
    
    def get_workflow_info(self, workflow_type: str) -> Dict[str, Any]:
        """Get information about a specific workflow."""
        workflow_info = {
            "code_generation": {
                "description": "Generate code with basic analysis",
                "steps": ["generate_code", "analyze_code", "finalize"],
                "outputs": ["code", "analysis"]
            },
            "code_analysis": {
                "description": "Analyze existing code and suggest improvements",
                "steps": ["analyze_code", "suggest_improvements", "finalize"],
                "outputs": ["analysis", "improvements"]
            },
            "full_development": {
                "description": "Complete development workflow with code, tests, and review",
                "steps": ["generate_code", "analyze_code", "generate_tests", "review_and_improve", "finalize"],
                "outputs": ["code", "analysis", "tests", "review"]
            }
        }
        
        return workflow_info.get(workflow_type, {})
