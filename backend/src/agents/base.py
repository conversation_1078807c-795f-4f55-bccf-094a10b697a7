"""
Base agent classes and interfaces for the Agentic Code Generation System.
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from langgraph.graph import StateGraph, END
from pydantic import BaseModel, Field

from src.core.logging import LoggerMixin, log_agent_action
from src.llm.manager import LLMManager, LLMRequest
from src.context.engine import ContextEngine


class AgentState(BaseModel):
    """State object passed between agents in a workflow."""
    task_id: str
    messages: List[Dict[str, str]] = Field(default_factory=list)
    context: Dict[str, Any] = Field(default_factory=dict)
    artifacts: Dict[str, Any] = Field(default_factory=dict)
    current_step: str = "start"
    completed_steps: List[str] = Field(default_factory=list)
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentResult(BaseModel):
    """Result from an agent execution."""
    success: bool
    output: Any
    artifacts: Dict[str, Any] = Field(default_factory=dict)
    next_step: Optional[str] = None
    error: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class BaseAgent(ABC, LoggerMixin):
    """Base class for all agents in the system."""
    
    def __init__(
        self,
        name: str,
        llm_manager: LLMManager,
        context_engine: ContextEngine,
        config: Optional[Dict[str, Any]] = None
    ):
        self.name = name
        self.llm_manager = llm_manager
        self.context_engine = context_engine
        self.config = config or {}
        self.capabilities = self._define_capabilities()
        
    @abstractmethod
    def _define_capabilities(self) -> List[str]:
        """Define the capabilities of this agent."""
        pass
    
    @abstractmethod
    async def execute(self, state: AgentState) -> AgentResult:
        """Execute the agent's main functionality."""
        pass
    
    async def _llm_request(
        self,
        messages: List[Dict[str, str]],
        provider: Optional[str] = None,
        **kwargs
    ) -> str:
        """Make an LLM request with logging."""
        self.logger.info(
            "Making LLM request",
            **log_agent_action(self.name, "llm_request", message_count=len(messages))
        )
        
        request = LLMRequest(
            messages=messages,
            provider=provider,
            **kwargs
        )
        
        response = await self.llm_manager.complete(request)
        return response.content
    
    async def _add_to_context(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """Add information to the context engine."""
        metadata = metadata or {}
        metadata.update({
            "agent": self.name,
            "timestamp": datetime.now().isoformat()
        })
        
        return await self.context_engine.add_to_working_memory(content, metadata)
    
    async def _search_context(self, query: str, limit: int = 5) -> List[Any]:
        """Search the context engine."""
        return await self.context_engine.search_memory(query, limit=limit)
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt for this agent."""
        return f"""You are {self.name}, an AI agent specialized in {', '.join(self.capabilities)}.
        
Your role is to help with software development tasks by providing expert assistance in your area of specialization.
Always provide clear, actionable, and well-reasoned responses.
Focus on producing high-quality, production-ready solutions.
"""
    
    async def _validate_input(self, state: AgentState) -> bool:
        """Validate input state for this agent."""
        return True
    
    async def _prepare_context(self, state: AgentState) -> str:
        """Prepare context information for the agent."""
        context_parts = []
        
        # Add task context
        if state.context:
            context_parts.append("Current Context:")
            for key, value in state.context.items():
                context_parts.append(f"- {key}: {value}")
        
        # Add artifacts
        if state.artifacts:
            context_parts.append("\nAvailable Artifacts:")
            for key, value in state.artifacts.items():
                if isinstance(value, str) and len(value) > 200:
                    context_parts.append(f"- {key}: {value[:200]}...")
                else:
                    context_parts.append(f"- {key}: {value}")
        
        # Search for relevant context
        if state.messages:
            last_message = state.messages[-1].get("content", "")
            relevant_context = await self._search_context(last_message)
            if relevant_context:
                context_parts.append("\nRelevant Context:")
                for item in relevant_context[:3]:  # Top 3 most relevant
                    context_parts.append(f"- {item.content[:150]}...")
        
        return "\n".join(context_parts)


class CodeGeneratorAgent(BaseAgent):
    """Agent specialized in generating code from specifications."""
    
    def _define_capabilities(self) -> List[str]:
        return ["code_generation", "language_translation", "boilerplate_creation"]
    
    async def execute(self, state: AgentState) -> AgentResult:
        """Generate code based on the current state."""
        try:
            if not await self._validate_input(state):
                return AgentResult(
                    success=False,
                    output=None,
                    error="Invalid input state"
                )
            
            # Prepare context
            context = await self._prepare_context(state)
            
            # Get the code generation request
            if not state.messages:
                return AgentResult(
                    success=False,
                    output=None,
                    error="No messages provided"
                )
            
            last_message = state.messages[-1]["content"]
            language = state.context.get("language", "python")
            
            # Create messages for LLM
            messages = [
                {"role": "system", "content": self._create_system_prompt()},
                {"role": "user", "content": f"Context:\n{context}"},
                {"role": "user", "content": f"Generate {language} code for: {last_message}"}
            ]
            
            # Generate code
            generated_code = await self._llm_request(messages, temperature=0.1)
            
            # Add to context
            await self._add_to_context(
                f"Generated {language} code: {generated_code}",
                {"type": "generated_code", "language": language}
            )
            
            return AgentResult(
                success=True,
                output=generated_code,
                artifacts={"generated_code": generated_code, "language": language},
                next_step="code_review"
            )
            
        except Exception as e:
            self.logger.error(
                "Code generation failed",
                **log_agent_action(self.name, "execute", error=str(e))
            )
            return AgentResult(
                success=False,
                output=None,
                error=str(e)
            )


class CodeAnalyzerAgent(BaseAgent):
    """Agent specialized in analyzing and reviewing code."""
    
    def _define_capabilities(self) -> List[str]:
        return ["code_analysis", "bug_detection", "quality_assessment", "security_review"]
    
    async def execute(self, state: AgentState) -> AgentResult:
        """Analyze code from the current state."""
        try:
            # Get code to analyze
            code = state.artifacts.get("generated_code") or state.context.get("code")
            if not code:
                return AgentResult(
                    success=False,
                    output=None,
                    error="No code provided for analysis"
                )
            
            language = state.context.get("language", "python")
            analysis_type = state.context.get("analysis_type", "general")
            
            # Analyze code using LLM
            analysis = await self.llm_manager.analyze_code(
                code=code,
                language=language,
                analysis_type=analysis_type
            )
            
            # Add to context
            await self._add_to_context(
                f"Code analysis result: {analysis}",
                {"type": "code_analysis", "language": language, "analysis_type": analysis_type}
            )
            
            return AgentResult(
                success=True,
                output=analysis,
                artifacts={"analysis": analysis, "analyzed_code": code},
                next_step="improvement_suggestions"
            )
            
        except Exception as e:
            self.logger.error(
                "Code analysis failed",
                **log_agent_action(self.name, "execute", error=str(e))
            )
            return AgentResult(
                success=False,
                output=None,
                error=str(e)
            )


class TestGeneratorAgent(BaseAgent):
    """Agent specialized in generating test cases."""
    
    def _define_capabilities(self) -> List[str]:
        return ["test_generation", "test_planning", "coverage_analysis"]
    
    async def execute(self, state: AgentState) -> AgentResult:
        """Generate tests for the provided code."""
        try:
            code = state.artifacts.get("generated_code") or state.context.get("code")
            if not code:
                return AgentResult(
                    success=False,
                    output=None,
                    error="No code provided for test generation"
                )
            
            language = state.context.get("language", "python")
            test_framework = state.context.get("test_framework", "pytest")
            
            # Prepare context
            context = await self._prepare_context(state)
            
            # Create messages for test generation
            messages = [
                {"role": "system", "content": self._create_system_prompt()},
                {"role": "user", "content": f"Context:\n{context}"},
                {"role": "user", "content": f"""
                Generate comprehensive {test_framework} tests for this {language} code:
                
                ```{language}
                {code}
                ```
                
                Include:
                - Unit tests for all functions/methods
                - Edge cases and error conditions
                - Mock objects where appropriate
                - Clear test documentation
                """}
            ]
            
            # Generate tests
            test_code = await self._llm_request(messages, temperature=0.2)
            
            # Add to context
            await self._add_to_context(
                f"Generated {test_framework} tests: {test_code}",
                {"type": "test_code", "language": language, "framework": test_framework}
            )
            
            return AgentResult(
                success=True,
                output=test_code,
                artifacts={"test_code": test_code, "test_framework": test_framework},
                next_step="documentation"
            )
            
        except Exception as e:
            self.logger.error(
                "Test generation failed",
                **log_agent_action(self.name, "execute", error=str(e))
            )
            return AgentResult(
                success=False,
                output=None,
                error=str(e)
            )
