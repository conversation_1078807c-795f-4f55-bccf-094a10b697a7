"""
Main FastAPI application entry point for the Agentic Code Generation System.
"""

import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware

from src.api.routes import agents, code, context, health
from src.core.config import get_settings
from src.core.logging import setup_logging
from src.context.engine import ContextEngine
from src.llm.manager import LLMManager

# Setup structured logging
setup_logging()
logger = structlog.get_logger(__name__)

settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager for startup and shutdown events."""
    logger.info("Starting Agentic Code Generation System")
    
    try:
        # Initialize core components
        logger.info("Initializing LLM Manager")
        llm_manager = LLMManager()
        await llm_manager.initialize()
        app.state.llm_manager = llm_manager
        
        logger.info("Initializing Context Engine")
        context_engine = ContextEngine()
        await context_engine.initialize()
        app.state.context_engine = context_engine
        
        logger.info("System initialization complete")
        yield
        
    except Exception as e:
        logger.error("Failed to initialize system", error=str(e))
        raise
    finally:
        # Cleanup
        logger.info("Shutting down system")
        if hasattr(app.state, 'context_engine'):
            await app.state.context_engine.cleanup()
        if hasattr(app.state, 'llm_manager'):
            await app.state.llm_manager.cleanup()
        logger.info("System shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title="Agentic Code Generation System",
        description="AI-powered code generation and analysis system with multi-agent orchestration",
        version="1.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Include routers
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(agents.router, prefix="/api/v1/agents", tags=["agents"])
    app.include_router(code.router, prefix="/api/v1/code", tags=["code"])
    app.include_router(context.router, prefix="/api/v1/context", tags=["context"])
    
    @app.get("/")
    async def root():
        """Root endpoint with system information."""
        return {
            "name": "Agentic Code Generation System",
            "version": "1.0.0",
            "status": "running",
            "docs": "/docs" if settings.debug else "disabled"
        }
    
    return app


# Create the application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info"
    )
