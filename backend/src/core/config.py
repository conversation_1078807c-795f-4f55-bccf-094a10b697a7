"""
Configuration management for the Agentic Code Generation System.
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # Application
    app_name: str = "Agentic Code Generation System"
    debug: bool = Field(default=False, description="Enable debug mode")
    host: str = Field(default="0.0.0.0", description="Host to bind the server")
    port: int = Field(default=8000, description="Port to bind the server")
    allowed_origins: List[str] = Field(
        default=["http://localhost:3000", "vscode-webview://*"],
        description="Allowed CORS origins"
    )
    
    # Security
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="Secret key for JWT tokens"
    )
    access_token_expire_minutes: int = Field(
        default=30,
        description="Access token expiration time in minutes"
    )
    
    # LLM Configuration
    default_llm_provider: str = Field(
        default="openai",
        description="Default LLM provider (openai, anthropic, etc.)"
    )
    openai_api_key: Optional[str] = Field(
        default=None,
        description="OpenAI API key"
    )
    anthropic_api_key: Optional[str] = Field(
        default=None,
        description="Anthropic API key"
    )
    max_tokens: int = Field(
        default=4000,
        description="Maximum tokens for LLM responses"
    )
    temperature: float = Field(
        default=0.1,
        description="LLM temperature for code generation"
    )
    
    # Vector Database (ChromaDB)
    chroma_host: str = Field(
        default="localhost",
        description="ChromaDB host"
    )
    chroma_port: int = Field(
        default=8001,
        description="ChromaDB port"
    )
    chroma_collection_name: str = Field(
        default="agentic_code",
        description="ChromaDB collection name"
    )
    embedding_model: str = Field(
        default="all-MiniLM-L6-v2",
        description="Sentence transformer model for embeddings"
    )
    
    # Graph Database (Neo4j)
    neo4j_uri: str = Field(
        default="bolt://localhost:7687",
        description="Neo4j connection URI"
    )
    neo4j_user: str = Field(
        default="neo4j",
        description="Neo4j username"
    )
    neo4j_password: str = Field(
        default="password",
        description="Neo4j password"
    )
    
    # Redis Cache
    redis_url: str = Field(
        default="redis://localhost:6379",
        description="Redis connection URL"
    )
    cache_ttl: int = Field(
        default=3600,
        description="Cache TTL in seconds"
    )
    
    # Context Engine
    max_context_length: int = Field(
        default=16000,
        description="Maximum context length for memory"
    )
    working_memory_size: int = Field(
        default=10,
        description="Working memory size (number of items)"
    )
    short_term_memory_size: int = Field(
        default=100,
        description="Short-term memory size (number of items)"
    )
    memory_consolidation_threshold: float = Field(
        default=0.8,
        description="Threshold for memory consolidation"
    )
    
    # Agent Configuration
    max_agent_iterations: int = Field(
        default=10,
        description="Maximum iterations for agent workflows"
    )
    agent_timeout: int = Field(
        default=300,
        description="Agent timeout in seconds"
    )
    
    # Code Analysis
    supported_languages: List[str] = Field(
        default=[
            "python", "typescript", "javascript", "java", "cpp", "c",
            "go", "rust", "php", "ruby", "swift", "kotlin", "scala",
            "csharp", "html", "css", "sql", "yaml", "json", "markdown"
        ],
        description="Supported programming languages"
    )
    max_file_size: int = Field(
        default=1024 * 1024,  # 1MB
        description="Maximum file size for analysis in bytes"
    )
    
    # Logging
    log_level: str = Field(
        default="INFO",
        description="Logging level"
    )
    log_format: str = Field(
        default="json",
        description="Log format (json or console)"
    )
    
    # Performance
    max_concurrent_requests: int = Field(
        default=100,
        description="Maximum concurrent requests"
    )
    request_timeout: int = Field(
        default=60,
        description="Request timeout in seconds"
    )
    
    # Development
    auto_reload: bool = Field(
        default=False,
        description="Enable auto-reload in development"
    )
    
    @property
    def database_url(self) -> str:
        """Get database URL for SQLAlchemy."""
        return f"sqlite:///./agentic.db"
    
    def get_llm_config(self, provider: Optional[str] = None) -> dict:
        """Get LLM configuration for the specified provider."""
        provider = provider or self.default_llm_provider
        
        config = {
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
        }
        
        if provider == "openai":
            config["api_key"] = self.openai_api_key
        elif provider == "anthropic":
            config["api_key"] = self.anthropic_api_key
        
        return config


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
