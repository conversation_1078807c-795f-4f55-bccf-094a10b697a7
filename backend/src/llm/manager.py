"""
LLM Manager for handling multiple LLM providers using LiteLLM.
"""

import asyncio
from typing import Any, Dict, List, Optional, Union

import litellm
from litellm import acompletion, completion
from pydantic import BaseModel

from src.core.config import get_settings
from src.core.logging import LoggerMixin, log_llm_request


class LLMRequest(BaseModel):
    """Request model for LLM interactions."""
    messages: List[Dict[str, str]]
    model: Optional[str] = None
    provider: Optional[str] = None
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    stream: bool = False
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None


class LLMResponse(BaseModel):
    """Response model for LLM interactions."""
    content: str
    model: str
    provider: str
    usage: Dict[str, int]
    finish_reason: str
    tool_calls: Optional[List[Dict[str, Any]]] = None


class LLMManager(LoggerMixin):
    """Manager for handling multiple LLM providers and models."""
    
    def __init__(self):
        self.settings = get_settings()
        self.providers = {}
        self.default_models = {
            "openai": "gpt-4-turbo-preview",
            "anthropic": "claude-3-sonnet-20240229",
            "google": "gemini-pro",
            "cohere": "command-r-plus",
            "ollama": "codellama:7b",
        }
        
    async def initialize(self) -> None:
        """Initialize the LLM manager and configure providers."""
        self.logger.info("Initializing LLM Manager")
        
        # Configure LiteLLM
        litellm.set_verbose = self.settings.debug
        
        # Set up API keys
        if self.settings.openai_api_key:
            litellm.openai_key = self.settings.openai_api_key
            self.providers["openai"] = True
            self.logger.info("OpenAI provider configured")
            
        if self.settings.anthropic_api_key:
            litellm.anthropic_key = self.settings.anthropic_api_key
            self.providers["anthropic"] = True
            self.logger.info("Anthropic provider configured")
        
        # Test default provider
        await self._test_provider(self.settings.default_llm_provider)
        
        self.logger.info(
            "LLM Manager initialized",
            providers=list(self.providers.keys()),
            default_provider=self.settings.default_llm_provider
        )
    
    async def _test_provider(self, provider: str) -> bool:
        """Test if a provider is working correctly."""
        try:
            model = self.default_models.get(provider)
            if not model:
                return False
                
            response = await acompletion(
                model=model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            
            self.logger.info(f"Provider {provider} test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"Provider {provider} test failed", error=str(e))
            return False
    
    def _get_model_name(self, provider: str, model: Optional[str] = None) -> str:
        """Get the full model name for a provider."""
        if model:
            return model
        return self.default_models.get(provider, "gpt-3.5-turbo")
    
    async def complete(self, request: LLMRequest) -> LLMResponse:
        """Generate a completion using the specified or default LLM."""
        provider = request.provider or self.settings.default_llm_provider
        model = self._get_model_name(provider, request.model)
        
        # Prepare completion parameters
        params = {
            "model": model,
            "messages": request.messages,
            "max_tokens": request.max_tokens or self.settings.max_tokens,
            "temperature": request.temperature or self.settings.temperature,
            "stream": request.stream,
        }
        
        # Add tools if provided
        if request.tools:
            params["tools"] = request.tools
        if request.tool_choice:
            params["tool_choice"] = request.tool_choice
        
        self.logger.info(
            "Making LLM request",
            **log_llm_request(
                provider=provider,
                model=model,
                tokens=params["max_tokens"]
            )
        )
        
        try:
            if request.stream:
                return await self._stream_completion(params)
            else:
                response = await acompletion(**params)
                return self._parse_response(response, provider)
                
        except Exception as e:
            self.logger.error(
                "LLM request failed",
                error=str(e),
                provider=provider,
                model=model
            )
            raise
    
    async def _stream_completion(self, params: Dict[str, Any]) -> LLMResponse:
        """Handle streaming completion."""
        # For now, convert streaming to non-streaming
        # TODO: Implement proper streaming support
        params["stream"] = False
        response = await acompletion(**params)
        return self._parse_response(response, params.get("model", "").split("/")[0])
    
    def _parse_response(self, response: Any, provider: str) -> LLMResponse:
        """Parse LiteLLM response into our response model."""
        choice = response.choices[0]
        message = choice.message
        
        return LLMResponse(
            content=message.content or "",
            model=response.model,
            provider=provider,
            usage={
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens,
            },
            finish_reason=choice.finish_reason,
            tool_calls=getattr(message, "tool_calls", None)
        )
    
    async def generate_code(
        self,
        prompt: str,
        language: str,
        context: Optional[str] = None,
        provider: Optional[str] = None
    ) -> str:
        """Generate code using the LLM."""
        messages = [
            {
                "role": "system",
                "content": f"""You are an expert {language} developer. Generate high-quality, 
                production-ready code that follows best practices and includes proper error handling, 
                documentation, and type hints where applicable."""
            }
        ]
        
        if context:
            messages.append({
                "role": "user",
                "content": f"Context:\n{context}\n\nRequest:\n{prompt}"
            })
        else:
            messages.append({
                "role": "user",
                "content": prompt
            })
        
        request = LLMRequest(
            messages=messages,
            provider=provider,
            temperature=0.1  # Lower temperature for code generation
        )
        
        response = await self.complete(request)
        return response.content
    
    async def analyze_code(
        self,
        code: str,
        language: str,
        analysis_type: str = "general",
        provider: Optional[str] = None
    ) -> str:
        """Analyze code using the LLM."""
        analysis_prompts = {
            "general": "Analyze this code for quality, potential issues, and improvements.",
            "security": "Analyze this code for security vulnerabilities and risks.",
            "performance": "Analyze this code for performance issues and optimization opportunities.",
            "bugs": "Analyze this code for potential bugs and logical errors.",
            "style": "Analyze this code for style and formatting issues."
        }
        
        prompt = analysis_prompts.get(analysis_type, analysis_prompts["general"])
        
        messages = [
            {
                "role": "system",
                "content": f"""You are an expert {language} code reviewer. Provide detailed, 
                actionable feedback on the code quality, potential issues, and suggested improvements."""
            },
            {
                "role": "user",
                "content": f"{prompt}\n\n```{language}\n{code}\n```"
            }
        ]
        
        request = LLMRequest(
            messages=messages,
            provider=provider,
            temperature=0.2
        )
        
        response = await self.complete(request)
        return response.content
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        self.logger.info("Cleaning up LLM Manager")
        # No specific cleanup needed for LiteLLM
        pass
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers."""
        return list(self.providers.keys())
    
    def get_provider_models(self, provider: str) -> List[str]:
        """Get available models for a provider."""
        # This would typically query the provider's API
        # For now, return default models
        models_map = {
            "openai": ["gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo"],
            "anthropic": ["claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
            "google": ["gemini-pro", "gemini-pro-vision"],
            "cohere": ["command-r-plus", "command-r"],
            "ollama": ["codellama:7b", "llama2:7b", "mistral:7b"],
        }
        
        return models_map.get(provider, [])
