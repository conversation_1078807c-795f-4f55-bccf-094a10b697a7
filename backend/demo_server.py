"""
Simple demo server to showcase the Agentic Code Generation System capabilities.
"""

import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import litellm

# Set up environment
os.environ.setdefault("OPENAI_API_KEY", "sk-placeholder")
os.environ.setdefault("ANTHROPIC_API_KEY", "sk-ant-placeholder")

app = FastAPI(
    title="Agentic Code Generation System - Demo",
    description="AI-powered code generation with multi-agent orchestration",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response Models
class CodeGenerationRequest(BaseModel):
    prompt: str
    language: str = "python"
    include_tests: bool = False
    include_analysis: bool = True
    context: Dict[str, Any] = {}

class CodeAnalysisRequest(BaseModel):
    code: str
    language: str = "python"
    analysis_type: str = "general"

class WorkflowResult(BaseModel):
    task_id: str
    success: bool
    outputs: Dict[str, Any]
    artifacts: Dict[str, Any] = {}
    execution_log: List[Dict[str, Any]] = []
    error: Optional[str] = None

class ToolCall(BaseModel):
    """Tool call structure for function calling."""
    name: str
    arguments: Dict[str, Any]
    call_id: str

class ModelContextRequest(BaseModel):
    """Model Context Protocol request."""
    method: str
    params: Dict[str, Any]

# Mock LLM function for demo (replace with actual API keys)
async def mock_llm_call(prompt: str, tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
    """Mock LLM call for demonstration."""
    if "fibonacci" in prompt.lower():
        return {
            "content": """def fibonacci(n):
    \"\"\"Calculate the nth Fibonacci number.
    
    Args:
        n (int): The position in the Fibonacci sequence
        
    Returns:
        int: The nth Fibonacci number
    \"\"\"
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return fibonacci(n-1) + fibonacci(n-2)

# Example usage
if __name__ == "__main__":
    for i in range(10):
        print(f"F({i}) = {fibonacci(i)}")""",
            "tool_calls": [
                {
                    "name": "code_analyzer",
                    "arguments": {"code": "fibonacci function", "language": "python"},
                    "call_id": "call_123"
                }
            ] if tools else None
        }
    elif "analyze" in prompt.lower():
        return {
            "content": """Code Analysis Results:

**Quality Assessment: 8/10**

**Strengths:**
- Clear function documentation with docstring
- Proper type hints implied
- Good variable naming
- Includes example usage

**Areas for Improvement:**
- Recursive implementation is inefficient for large n (O(2^n) time complexity)
- No input validation for negative numbers
- Could benefit from memoization or iterative approach

**Suggestions:**
1. Add input validation: `if not isinstance(n, int) or n < 0: raise ValueError("n must be a non-negative integer")`
2. Consider iterative implementation for better performance
3. Add type hints: `def fibonacci(n: int) -> int:`

**Security:** No security concerns identified.
**Performance:** Consider optimization for large inputs.""",
            "tool_calls": None
        }
    else:
        return {
            "content": f"Generated response for: {prompt}",
            "tool_calls": None
        }

@app.get("/")
async def root():
    return {
        "name": "Agentic Code Generation System",
        "version": "1.0.0",
        "status": "running",
        "features": [
            "Multi-agent code generation",
            "Tool calling support",
            "Model Context Protocol",
            "Brain-like memory system",
            "Multi-language support"
        ]
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "llm_manager": "ready",
            "context_engine": "ready", 
            "agent_orchestrator": "ready"
        }
    }

@app.post("/api/v1/agents/generate-code", response_model=WorkflowResult)
async def generate_code(request: CodeGenerationRequest):
    """Generate code using the agent system with tool calling support."""
    try:
        task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Define available tools for the LLM
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "code_analyzer",
                    "description": "Analyze code for quality, bugs, and improvements",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "code": {"type": "string", "description": "Code to analyze"},
                            "language": {"type": "string", "description": "Programming language"},
                            "analysis_type": {"type": "string", "enum": ["general", "security", "performance"]}
                        },
                        "required": ["code", "language"]
                    }
                }
            },
            {
                "type": "function", 
                "function": {
                    "name": "test_generator",
                    "description": "Generate test cases for code",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "code": {"type": "string", "description": "Code to test"},
                            "language": {"type": "string", "description": "Programming language"},
                            "framework": {"type": "string", "description": "Testing framework"}
                        },
                        "required": ["code", "language"]
                    }
                }
            }
        ]
        
        # Generate code with tool calling
        code_prompt = f"Generate {request.language} code for: {request.prompt}"
        llm_response = await mock_llm_call(code_prompt, tools if request.include_analysis else None)
        
        outputs = {"code": llm_response["content"]}
        artifacts = {"generated_code": llm_response["content"]}
        execution_log = [{"step": "code_generation", "status": "completed", "timestamp": datetime.now().isoformat()}]
        
        # Handle tool calls if present
        if llm_response.get("tool_calls") and request.include_analysis:
            for tool_call in llm_response["tool_calls"]:
                if tool_call["name"] == "code_analyzer":
                    analysis_response = await mock_llm_call("analyze the generated code")
                    outputs["analysis"] = analysis_response["content"]
                    artifacts["analysis"] = analysis_response["content"]
                    execution_log.append({
                        "step": "code_analysis", 
                        "status": "completed", 
                        "timestamp": datetime.now().isoformat(),
                        "tool_call": tool_call
                    })
        
        # Generate tests if requested
        if request.include_tests:
            test_response = await mock_llm_call(f"Generate tests for {request.language} code")
            outputs["tests"] = test_response["content"]
            artifacts["test_code"] = test_response["content"]
            execution_log.append({"step": "test_generation", "status": "completed", "timestamp": datetime.now().isoformat()})
        
        return WorkflowResult(
            task_id=task_id,
            success=True,
            outputs=outputs,
            artifacts=artifacts,
            execution_log=execution_log
        )
        
    except Exception as e:
        return WorkflowResult(
            task_id=task_id,
            success=False,
            outputs={},
            error=str(e)
        )

@app.post("/api/v1/agents/analyze-code", response_model=WorkflowResult)
async def analyze_code(request: CodeAnalysisRequest):
    """Analyze code using the agent system."""
    try:
        task_id = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        analysis_prompt = f"Analyze this {request.language} code for {request.analysis_type} issues:\n\n{request.code}"
        llm_response = await mock_llm_call(analysis_prompt)
        
        return WorkflowResult(
            task_id=task_id,
            success=True,
            outputs={"analysis": llm_response["content"]},
            artifacts={"analysis": llm_response["content"], "analyzed_code": request.code},
            execution_log=[{"step": "code_analysis", "status": "completed", "timestamp": datetime.now().isoformat()}]
        )
        
    except Exception as e:
        return WorkflowResult(
            task_id=task_id,
            success=False,
            outputs={},
            error=str(e)
        )

@app.post("/api/v1/tools/call")
async def call_tool(tool_call: ToolCall):
    """Execute a tool call - demonstrates tool calling capability."""
    try:
        if tool_call.name == "code_analyzer":
            code = tool_call.arguments.get("code", "")
            language = tool_call.arguments.get("language", "python")
            analysis_type = tool_call.arguments.get("analysis_type", "general")
            
            result = await mock_llm_call(f"Analyze {language} code: {code}")
            
            return {
                "call_id": tool_call.call_id,
                "result": result["content"],
                "success": True
            }
        
        elif tool_call.name == "test_generator":
            code = tool_call.arguments.get("code", "")
            language = tool_call.arguments.get("language", "python")
            framework = tool_call.arguments.get("framework", "pytest")
            
            test_code = f"""import {framework}
from your_module import fibonacci

def test_fibonacci_base_cases():
    assert fibonacci(0) == 0
    assert fibonacci(1) == 1

def test_fibonacci_sequence():
    assert fibonacci(2) == 1
    assert fibonacci(3) == 2
    assert fibonacci(4) == 3
    assert fibonacci(5) == 5

def test_fibonacci_larger_numbers():
    assert fibonacci(10) == 55
    assert fibonacci(15) == 610
"""
            
            return {
                "call_id": tool_call.call_id,
                "result": test_code,
                "success": True
            }
        
        else:
            return {
                "call_id": tool_call.call_id,
                "result": None,
                "success": False,
                "error": f"Unknown tool: {tool_call.name}"
            }
            
    except Exception as e:
        return {
            "call_id": tool_call.call_id,
            "result": None,
            "success": False,
            "error": str(e)
        }

@app.post("/api/v1/mcp")
async def model_context_protocol(request: ModelContextRequest):
    """Model Context Protocol endpoint for advanced context management."""
    try:
        if request.method == "context/get":
            # Return current context
            return {
                "context": {
                    "working_memory": [
                        {"id": "wm_1", "content": "Current task: code generation", "importance": 0.9},
                        {"id": "wm_2", "content": "Language: python", "importance": 0.8}
                    ],
                    "short_term_memory": [
                        {"id": "stm_1", "content": "Recent fibonacci function generation", "importance": 0.7}
                    ],
                    "long_term_memory": {
                        "patterns": ["fibonacci", "recursion", "dynamic_programming"],
                        "knowledge_base": "Code generation patterns and best practices"
                    }
                }
            }
        
        elif request.method == "context/add":
            content = request.params.get("content", "")
            memory_type = request.params.get("memory_type", "working")
            
            return {
                "success": True,
                "message": f"Added to {memory_type} memory: {content[:50]}..."
            }
        
        elif request.method == "context/search":
            query = request.params.get("query", "")
            
            # Mock search results
            results = [
                {
                    "id": "result_1",
                    "content": f"Found relevant context for: {query}",
                    "relevance": 0.85,
                    "memory_type": "long_term"
                }
            ]
            
            return {"results": results}
        
        elif request.method == "tools/list":
            return {
                "tools": [
                    {
                        "name": "code_analyzer",
                        "description": "Analyze code for quality and issues",
                        "parameters": ["code", "language", "analysis_type"]
                    },
                    {
                        "name": "test_generator", 
                        "description": "Generate test cases for code",
                        "parameters": ["code", "language", "framework"]
                    }
                ]
            }
        
        else:
            return {
                "error": f"Unknown method: {request.method}",
                "supported_methods": ["context/get", "context/add", "context/search", "tools/list"]
            }
            
    except Exception as e:
        return {"error": str(e)}

@app.get("/api/v1/capabilities")
async def get_capabilities():
    """Get system capabilities including tool calling and MCP support."""
    return {
        "tool_calling": {
            "supported": True,
            "available_tools": [
                "code_analyzer",
                "test_generator", 
                "documentation_generator",
                "refactoring_assistant"
            ],
            "formats": ["openai_functions", "anthropic_tools"]
        },
        "model_context_protocol": {
            "supported": True,
            "version": "1.0",
            "methods": [
                "context/get",
                "context/add", 
                "context/search",
                "tools/list",
                "tools/call"
            ]
        },
        "memory_system": {
            "types": ["working", "short_term", "long_term"],
            "brain_like_patterns": True,
            "consolidation": True,
            "semantic_search": True
        },
        "agent_orchestration": {
            "framework": "LangGraph",
            "multi_agent": True,
            "workflows": ["code_generation", "analysis", "full_development"]
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
