# Core Framework
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# LLM and AI
litellm>=1.17.9
langchain>=0.1.0
langgraph>=0.0.26
openai>=1.6.1
anthropic>=0.8.1

# Vector Database and Embeddings
chromadb>=0.4.18
sentence-transformers>=2.2.2
tiktoken>=0.5.2

# Graph Database
neo4j>=5.15.0
py2neo>=2021.2.4

# Caching and Storage
redis>=5.0.1
sqlalchemy>=2.0.23
alembic>=1.13.1

# HTTP and WebSocket
httpx>=0.25.2
websockets>=12.0
aiofiles>=23.2.1

# Data Processing
pandas>=2.2.0
numpy>=1.25.2
python-multipart>=0.0.6

# Utilities
python-dotenv==1.0.0
structlog==23.2.0
rich==13.7.0
typer==0.9.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Monitoring
prometheus-client==0.19.0
